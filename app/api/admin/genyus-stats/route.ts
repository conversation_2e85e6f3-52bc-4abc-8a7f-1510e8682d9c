import { NextRequest } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { getRateLimitStats, getCacheStats } from "@/lib/genyus/rate-limit";

export const runtime = "edge";

function createSbEdge() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );
}

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), { 
    status, 
    headers: { "Content-Type": "application/json" }
  });
}

export async function GET(req: NextRequest) {
  try {
    // Simple admin auth check (you can enhance this)
    const authHeader = req.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.ADMIN_SECRET}`) {
      return resp({ error: "Unauthorized" }, 401);
    }

    const supabase = createSbEdge();
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get request stats
    const { data: requestStats } = await supabase
      .from('genyus_requests')
      .select('created_at')
      .gte('created_at', last7d.toISOString());

    const requests24h = requestStats?.filter(r => 
      new Date(r.created_at) >= last24h
    ).length || 0;

    const requests7d = requestStats?.length || 0;

    // Get answer stats
    const { data: answerStats } = await supabase
      .from('genyus_answers')
      .select('word_count, latency_ms, created_at, safety_flags')
      .gte('created_at', last7d.toISOString());

    const answers24h = answerStats?.filter(a => 
      new Date(a.created_at) >= last24h
    ) || [];

    const answers7d = answerStats || [];

    // Calculate metrics
    const totalWords24h = answers24h.reduce((sum, a) => sum + (a.word_count || 0), 0);
    const totalWords7d = answers7d.reduce((sum, a) => sum + (a.word_count || 0), 0);
    
    const avgLatency24h = answers24h.length > 0 
      ? answers24h.reduce((sum, a) => sum + (a.latency_ms || 0), 0) / answers24h.length
      : 0;

    const avgLatency7d = answers7d.length > 0 
      ? answers7d.reduce((sum, a) => sum + (a.latency_ms || 0), 0) / answers7d.length
      : 0;

    const flaggedAnswers24h = answers24h.filter(a => a.safety_flags?.flagged).length;
    const flaggedAnswers7d = answers7d.filter(a => a.safety_flags?.flagged).length;

    // Get provider response stats
    const { data: providerStats } = await supabase
      .from('genyus_provider_responses')
      .select('provider, latency_ms, error, created_at')
      .gte('created_at', last24h.toISOString());

    const providerMetrics = ['openai', 'google', 'anthropic'].map(provider => {
      const responses = providerStats?.filter(p => p.provider === provider) || [];
      const errors = responses.filter(p => p.error).length;
      const avgLatency = responses.length > 0
        ? responses.reduce((sum, p) => sum + (p.latency_ms || 0), 0) / responses.length
        : 0;
      
      return {
        provider,
        requests: responses.length,
        errors,
        errorRate: responses.length > 0 ? (errors / responses.length) * 100 : 0,
        avgLatency: Math.round(avgLatency),
      };
    });

    // Get user stats
    const { data: userStats } = await supabase
      .from('genyus_user_words')
      .select('tier, words_remaining');

    const tierDistribution = userStats?.reduce((acc, user) => {
      acc[user.tier] = (acc[user.tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const totalUsers = userStats?.length || 0;
    const avgWordsRemaining = userStats && userStats.length > 0
      ? userStats.reduce((sum, u) => sum + u.words_remaining, 0) / userStats.length
      : 0;

    // Get rate limit and cache stats
    const rateLimitStats = getRateLimitStats();
    const cacheStats = getCacheStats();

    return resp({
      timestamp: now.toISOString(),
      requests: {
        last24h: requests24h,
        last7d: requests7d,
      },
      answers: {
        last24h: answers24h.length,
        last7d: answers7d.length,
      },
      words: {
        last24h: totalWords24h,
        last7d: totalWords7d,
      },
      latency: {
        avg24h: Math.round(avgLatency24h),
        avg7d: Math.round(avgLatency7d),
      },
      safety: {
        flagged24h: flaggedAnswers24h,
        flagged7d: flaggedAnswers7d,
        flagRate24h: answers24h.length > 0 ? (flaggedAnswers24h / answers24h.length) * 100 : 0,
      },
      providers: providerMetrics,
      users: {
        total: totalUsers,
        tierDistribution,
        avgWordsRemaining: Math.round(avgWordsRemaining),
      },
      system: {
        rateLimit: rateLimitStats,
        cache: cacheStats,
      },
    });

  } catch (error) {
    console.error('Admin stats error:', error);
    return resp({ error: "Internal server error" }, 500);
  }
}
