import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const search = searchParams.get('search') || ''

    // Build query for conversation history
    let query = supabase
      .from('genyus_requests')
      .select(`
        id,
        question,
        created_at,
        genyus_answers!inner(
          id,
          final_text,
          created_at
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Add search filter if provided
    if (search) {
      query = query.or(`question.ilike.%${search}%,genyus_answers.final_text.ilike.%${search}%`)
    }

    const { data: conversations, error: conversationsError } = await query

    if (conversationsError) {
      return NextResponse.json({ error: 'Failed to fetch conversations' }, { status: 500 })
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('genyus_requests')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (search) {
      countQuery = countQuery.or(`question.ilike.%${search}%`)
    }

    const { count: totalConversations } = await countQuery

    // Format conversations as message pairs
    const formattedConversations = (conversations || []).map(conv => {
      const answer = conv.genyus_answers[0]
      return {
        id: conv.id,
        question: {
          content: conv.question,
          timestamp: conv.created_at
        },
        answer: {
          content: answer.final_text,
          timestamp: answer.created_at
        }
      }
    })

    return NextResponse.json({
      success: true,
      conversations: formattedConversations,
      pagination: {
        total: totalConversations || 0,
        limit,
        offset,
        hasMore: (totalConversations || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Get personal history error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
