import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { processGenyusGroupRequest } from '@/lib/genyus/group-orchestrator'
import { ConversationStateManager, ResponseTimingManager, detectQuestionInMessage } from '@/lib/genyus/response-timing'
import { ConversationIntelligence } from '@/lib/genyus/conversation-intelligence'
import { NameMemorySystem } from '@/lib/genyus/name-memory'

const messageSchema = z.object({
  groupId: z.string().uuid(),
  message: z.string().min(1).max(8000),
})

export async function POST(request: NextRequest) {
  try {
    console.log('Group message API called')
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('Auth error:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Request body:', body)
    const { groupId, message } = messageSchema.parse(body)
    console.log('Parsed:', { groupId, message: message.substring(0, 50) + '...' })

    // Verify user is active member of the group
    const { data: membership, error: membershipError } = await supabase
      .from('genyus_group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single()

    console.log('Membership check:', { groupId, userId: user.id, membership, membershipError })

    if (!membership) {
      console.log('User not a member of group:', groupId, user.id)
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    // Ensure user has word balance initialized, then check
    const { data: userWords, error: wordsError } = await supabase
      .rpc('ensure_user_has_words', { p_user_id: user.id })
      .single()

    if (wordsError) {
      console.error('Error ensuring user words:', wordsError)
      return NextResponse.json({ error: 'Failed to check token balance' }, { status: 500 })
    }

    if (!userWords || (userWords.words_remaining <= 0 && userWords.words_remaining !== -1)) {
      return NextResponse.json({
        error: 'Insufficient tokens to send message',
        needsUpgrade: true
      }, { status: 402 })
    }

    // Note: We only check the current user's tokens above.
    // Other members can be out of tokens - they just can't send messages.
    // Users with tokens/unlimited plans can continue chatting.

    // Get group details (simplified query to avoid RLS issues)
    const { data: group, error: groupError } = await supabase
      .from('genyus_group_chats')
      .select('id, name')
      .eq('id', groupId)
      .eq('is_active', true)
      .single()

    console.log('Group lookup result:', { group, groupError })

    if (!group) {
      console.log('Group not found:', groupId)
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Get group members separately - using separate queries to avoid foreign key issues
    const { data: memberIds, error: membersError } = await supabase
      .from('genyus_group_members')
      .select('user_id')
      .eq('group_id', groupId)
      .eq('is_active', true)

    let members: any[] = []
    if (memberIds && memberIds.length > 0) {
      const userIds = memberIds.map(m => m.user_id)
      const { data: userDetails } = await supabase
        .from('users')
        .select('id, name, email')
        .in('id', userIds)

      members = memberIds.map(m => ({
        user_id: m.user_id,
        users: userDetails?.find(u => u.id === m.user_id) || { name: 'Unknown', email: '' }
      }))
    }

    console.log('Members lookup result:', { members, membersError })

    // Store user message first
    const { data: userMessage, error: messageError } = await supabase
      .from('genyus_group_messages')
      .insert({
        group_id: groupId,
        user_id: user.id,
        message_type: 'user',
        content: message
      })
      .select('id, created_at')
      .single()

    if (messageError) {
      return NextResponse.json({ error: 'Failed to store message' }, { status: 500 })
    }

    // Update conversation state
    const hasQuestion = detectQuestionInMessage(message)
    ConversationStateManager.onUserMessage(groupId, user.id, hasQuestion)

    // Get detailed member information for name learning
    // Use the member data we already fetched above
    const memberDetails = members.map(m => ({
      user_id: m.user_id,
      joined_at: new Date(), // We don't have this easily, but it's not critical
      users: m.users
    }))

    // Build name memory system
    const groupMemory = NameMemorySystem.buildGroupMemory(
      groupId,
      memberDetails || [],
      [] // Will be populated with recent history
    )

    // Use intelligent conversation analysis with real names
    const conversationContext = {
      recentMessages: [], // Will be populated from group history
      groupMembers: groupMemory.members.map(member => ({
        id: member.id,
        name: groupMemory.namePreferences[member.id] || member.name,
        isActive: true
      })),
      conversationState: {
        phase: 'discussion' as const,
        lastAIResponse: null, // Will be set from history
        pendingQuestions: [],
        activeParticipants: [user.id],
        conversationTopic: null,
        energyLevel: 'medium' as const,
        waitingFor: 'none' as const
      }
    }

    // Get recent message history for context
    const { data: recentHistory } = await supabase
      .from('genyus_group_messages')
      .select(`
        message_type,
        content,
        created_at,
        user_id
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: false })
      .limit(10)

    if (recentHistory) {
      // Update group memory with recent history
      const updatedMemory = NameMemorySystem.buildGroupMemory(
        groupId,
        memberDetails || [],
        recentHistory
      )

      conversationContext.recentMessages = [
        {
          userId: user.id,
          userName: updatedMemory.namePreferences[user.id] || user.email || 'User',
          content: message,
          timestamp: new Date(),
          messageType: 'user' as const
        },
        ...recentHistory.map(msg => ({
          userId: msg.user_id,
          userName: updatedMemory.namePreferences[msg.user_id] || msg.users.name || msg.users.email,
          content: msg.content,
          timestamp: new Date(msg.created_at),
          messageType: msg.message_type as 'user' | 'assistant'
        }))
      ]

      // Update last AI response time
      const lastAI = recentHistory.find(msg => msg.message_type === 'assistant')
      if (lastAI) {
        conversationContext.conversationState.lastAIResponse = new Date(lastAI.created_at)
      }
    }

    // Analyze conversation with intelligence
    const responseStrategy = ConversationIntelligence.analyzeConversationContext(conversationContext)

    if (!responseStrategy.shouldRespond) {
      // AI should wait - return waiting response
      return NextResponse.json({
        success: true,
        message: 'Message received, waiting for others to respond',
        waiting: true,
        strategy: responseStrategy
      })
    }

    // Process AI response through group orchestrator
    const response = await processGenyusGroupRequest({
      userId: user.id,
      groupId,
      message,
      groupContext: {
        name: group.name,
        members: (members || []).map((m: any) => ({
          id: m.user_id,
          name: m.users.name || m.users.email
        }))
      }
    })

    // Convert string stream to Uint8Array stream for Response
    const encoder = new TextEncoder()
    const transformedStream = new ReadableStream({
      async start(controller) {
        const reader = response.stream.getReader()

        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            // Send to all group members via WebSocket (implement in real-time system)
            controller.enqueue(encoder.encode(value))
          }
        } catch (error) {
          controller.error(error)
        } finally {
          controller.close()
        }
      }
    })

    // Update conversation state after AI response
    ConversationStateManager.onAIResponse(groupId)

    return new Response(transformedStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-store',
        'X-Group-Id': groupId,
        'X-Message-Id': userMessage.id,
        'X-Response-Timing': 'group-chat'
      }
    })

  } catch (error) {
    console.error('Group message error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Process delayed AI response (called by timing manager)
async function processDelayedAIResponse(
  groupId: string,
  originalMessage: string,
  group: any,
  userId: string
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient()

    // Process AI response
    const response = await processGenyusGroupRequest({
      userId,
      groupId,
      message: originalMessage,
      groupContext: {
        name: group.name,
        members: (members || []).map((m: any) => ({
          id: m.user_id,
          name: m.users.name || m.users.email
        }))
      }
    })

    // Store AI response in database
    let finalText = ''
    const reader = response.stream.getReader()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        finalText += value
      }
    } finally {
      reader.releaseLock()
    }

    // Store the complete AI response
    await supabase
      .from('genyus_group_messages')
      .insert({
        group_id: groupId,
        user_id: null, // AI message
        message_type: 'assistant',
        content: finalText
      })

    // Update conversation state
    ConversationStateManager.onAIResponse(groupId)

  } catch (error) {
    console.error('Delayed AI response error:', error)
  }
}
