import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const joinSchema = z.object({
  inviteCode: z.string().min(1),
})

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { inviteCode } = joinSchema.parse(body)

    // Find group by invite code
    const { data: group } = await supabase
      .from('genyus_group_chats')
      .select('id, name, max_members, is_active')
      .eq('invite_code', inviteCode)
      .eq('is_active', true)
      .single()

    if (!group) {
      return NextResponse.json({ error: 'Invalid or expired invite code' }, { status: 404 })
    }

    // Ensure user has word balance initialized, then check eligibility
    const { data: userWords, error: wordsError } = await supabase
      .rpc('ensure_user_has_words', { p_user_id: user.id })
      .single()

    if (wordsError) {
      console.error('Error ensuring user words:', wordsError)
      return NextResponse.json({ error: 'Failed to check token balance' }, { status: 500 })
    }

    if (!userWords || (userWords.words_remaining < 1000 && userWords.words_remaining !== -1)) {
      return NextResponse.json({
        error: 'Insufficient tokens to join group chat',
        needsUpgrade: true
      }, { status: 402 })
    }

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from('genyus_group_members')
      .select('id, is_active')
      .eq('group_id', group.id)
      .eq('user_id', user.id)
      .single()

    if (existingMember) {
      if (existingMember.is_active) {
        return NextResponse.json({ error: 'Already a member of this group' }, { status: 400 })
      } else {
        // Reactivate membership
        const { error: reactivateError } = await supabase
          .from('genyus_group_members')
          .update({ is_active: true, joined_at: new Date().toISOString() })
          .eq('id', existingMember.id)

        if (reactivateError) {
          return NextResponse.json({ error: 'Failed to rejoin group' }, { status: 500 })
        }

        return NextResponse.json({
          success: true,
          message: 'Successfully rejoined the group',
          group: {
            id: group.id,
            name: group.name
          }
        })
      }
    }

    // Check if group is at capacity
    const { count: memberCount } = await supabase
      .from('genyus_group_members')
      .select('*', { count: 'exact', head: true })
      .eq('group_id', group.id)
      .eq('is_active', true)

    if (memberCount && memberCount >= group.max_members) {
      return NextResponse.json({ error: 'Group is at maximum capacity' }, { status: 400 })
    }

    // Check if all current members have sufficient tokens
    const { data: eligibilityCheck, error: eligibilityError } = await supabase
      .rpc('check_group_token_eligibility', { group_id_param: group.id })

    if (eligibilityError) {
      console.error('Token eligibility check failed:', eligibilityError)
    } else if (eligibilityCheck) {
      const ineligibleMembers = eligibilityCheck.filter((member: any) => !member.is_eligible)
      if (ineligibleMembers.length > 0) {
        return NextResponse.json({ 
          error: 'Some group members have insufficient tokens. All members must upgrade to continue group chat.',
          needsUpgrade: true 
        }, { status: 402 })
      }
    }

    // Add user to group
    const { error: memberError } = await supabase
      .from('genyus_group_members')
      .insert({
        group_id: group.id,
        user_id: user.id
      })

    if (memberError) {
      return NextResponse.json({ error: 'Failed to join group' }, { status: 500 })
    }

    // Update any pending invitations to accepted
    await supabase
      .from('genyus_group_invites')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString()
      })
      .eq('group_id', group.id)
      .eq('invitee_user_id', user.id)
      .eq('status', 'pending')

    // Also update by invite code for anonymous invites
    await supabase
      .from('genyus_group_invites')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        invitee_user_id: user.id
      })
      .eq('invite_code', inviteCode)
      .is('invitee_user_id', null)

    // Notify other group members that someone joined
    const { data: otherMembers } = await supabase
      .from('genyus_group_members')
      .select('user_id')
      .eq('group_id', group.id)
      .eq('is_active', true)
      .neq('user_id', user.id)

    if (otherMembers && otherMembers.length > 0) {
      // Send notification to each existing member
      const notifications = otherMembers.map(member => ({
        user_id: member.user_id,
        type: 'group_member_joined',
        title: `${user.email?.split('@')[0] || 'Someone'} joined ${group.name}`,
        message: `Your group chat is ready! Start the conversation.`,
        data: {
          group_id: group.id,
          group_name: group.name,
          new_member_id: user.id,
          url: `/genyus?group=${group.id}`
        }
      }))

      await supabase.from('notifications').insert(notifications)
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully joined the group',
      group: {
        id: group.id,
        name: group.name
      }
    })

  } catch (error) {
    console.error('Group join error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
