import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { groupId } = await params

    console.log('Group API - Looking for group:', groupId, 'for user:', user.id)

    // First check if group exists
    const { data: groupExists } = await supabase
      .from('genyus_group_chats')
      .select('id, name')
      .eq('id', groupId)
      .single()

    console.log('Group exists check:', groupExists)

    if (!groupExists) {
      console.log('Group not found:', groupId)
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Verify user is member of the group
    const { data: membership, error: membershipError } = await supabase
      .from('genyus_group_members')
      .select('id, is_active')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .single()

    console.log('Membership check:', { membership, membershipError })

    if (!membership) {
      console.log('User not a member of group:', groupId, user.id)
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    if (!membership.is_active) {
      console.log('User membership is inactive:', groupId, user.id)
      return NextResponse.json({ error: 'Membership is inactive' }, { status: 403 })
    }

    // Get group details first
    console.log('Fetching group details...')
    const { data: group, error: groupError } = await supabase
      .from('genyus_group_chats')
      .select('id, name, invite_code, creator_user_id, created_at')
      .eq('id', groupId)
      .single()

    console.log('Group details query result:', { group, groupError })

    if (!group) {
      console.log('Group details query returned null - this is the 404 source')
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Get group members separately with manual join
    console.log('Fetching group members...')
    const { data: memberIds, error: memberIdsError } = await supabase
      .from('genyus_group_members')
      .select('user_id, joined_at')
      .eq('group_id', groupId)
      .eq('is_active', true)

    console.log('Member IDs query result:', { memberIds, memberIdsError })

    type MemberWithUser = {
      user_id: string
      joined_at: string
      users: {
        id?: string
        name: string
        email: string
        profile_picture_url: string | null
      }
    }
    let members: MemberWithUser[] = []
    if (memberIds && memberIds.length > 0) {
      // Get user details for each member
      const { data: userDetails, error: userDetailsError } = await supabase
        .from('users')
        .select('id, name, email, profile_picture_url')
        .in('id', memberIds.map(m => m.user_id))

      console.log('User details query result:', { userDetails, userDetailsError })

      // Combine member and user data
      members = memberIds.map(member => {
        const user = userDetails?.find(u => u.id === member.user_id)
        return {
          user_id: member.user_id,
          joined_at: member.joined_at,
          users: user || { name: 'Unknown', email: '', profile_picture_url: null }
        }
      })
    }

    console.log('Final members result:', { members })

    // Get recent messages (last 50) - without user join to avoid foreign key issues
    console.log('Fetching messages...')
    const { data: messages, error: messagesError } = await supabase
      .from('genyus_group_messages')
      .select(`
        id,
        message_type,
        content,
        created_at,
        user_id
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: true })
      .limit(50)

    console.log('Messages query result:', {
      messageCount: messages?.length,
      messagesError
    })

    // Get user details for message authors separately
    let messageUsers: any[] = []
    if (messages && messages.length > 0) {
      const userIds = [...new Set(messages.map(m => m.user_id))]
      const { data: messageUserDetails } = await supabase
        .from('users')
        .select('id, name, email, profile_picture_url')
        .in('id', userIds)

      messageUsers = messageUserDetails || []
      console.log('Message user details:', { messageUsers })
    }

    // Format response
    const formattedGroup = {
      id: group.id,
      name: group.name,
      inviteCode: group.invite_code,
      creatorUserId: group.creator_user_id,
      createdAt: group.created_at,
      members: (members || []).map((member: any) => {
        console.log('Formatting member for response:', member)
        return {
          userId: member.user_id,
          name: member.users?.name || member.users?.email || 'Unknown',
          email: member.users?.email || '',
          profilePictureUrl: member.users?.profile_picture_url || null,
          joinedAt: member.joined_at
        }
      }),
      messages: (messages || []).map(msg => {
        const user = messageUsers.find(u => u.id === msg.user_id)
        return {
          id: msg.id,
          type: msg.message_type,
          content: msg.content,
          timestamp: msg.created_at,
          user: {
            id: msg.user_id,
            name: user?.name || user?.email || 'Unknown',
            profilePictureUrl: user?.profile_picture_url
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      group: formattedGroup
    })

  } catch (error) {
    console.error('Get group error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { groupId } = await params

    // Verify user is the creator of the group
    const { data: group } = await supabase
      .from('genyus_group_chats')
      .select('creator_user_id')
      .eq('id', groupId)
      .single()

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    if (group.creator_user_id !== user.id) {
      return NextResponse.json({ error: 'Only the group creator can delete the group' }, { status: 403 })
    }

    // Delete the group (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('genyus_group_chats')
      .delete()
      .eq('id', groupId)

    if (deleteError) {
      console.error('Group deletion error:', deleteError)
      return NextResponse.json({ error: 'Failed to delete group' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Group deletion error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
