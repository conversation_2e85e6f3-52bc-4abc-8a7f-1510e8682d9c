import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const pollSchema = z.object({
  since: z.string().optional().nullable(), // ISO timestamp
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { groupId } = await params
    const body = await request.json()
    const { since } = pollSchema.parse(body)

    // Verify user is member of the group
    const { data: membership } = await supabase
      .from('genyus_group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single()

    if (!membership) {
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    // Build query for new messages
    let query = supabase
      .from('genyus_group_messages')
      .select(`
        id,
        message_type,
        content,
        created_at,
        user_id,
        users!inner(name, email, profile_picture_url)
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: true })

    // If 'since' timestamp provided, only get messages after that time
    if (since) {
      query = query.gt('created_at', since)
    } else {
      // If no 'since', get last 20 messages
      query = query.limit(20)
    }

    const { data: messages } = await query

    // Format messages
    const formattedMessages = (messages || []).map(msg => ({
      id: msg.id,
      type: msg.message_type,
      content: msg.content,
      timestamp: msg.created_at,
      user: {
        id: msg.user_id,
        name: msg.users.name || msg.users.email,
        profilePictureUrl: msg.users.profile_picture_url
      }
    }))

    return NextResponse.json({
      success: true,
      messages: formattedMessages
    })

  } catch (error) {
    console.error('Get group messages error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
