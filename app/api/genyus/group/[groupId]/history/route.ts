import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { groupId } = params
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '100')
    const offset = parseInt(searchParams.get('offset') || '0')
    const search = searchParams.get('search') || ''

    // Verify user is member of the group
    const { data: membership } = await supabase
      .from('genyus_group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single()

    if (!membership) {
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    // Get group info
    const { data: group } = await supabase
      .from('genyus_group_chats')
      .select('name, created_at')
      .eq('id', groupId)
      .single()

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Build query for conversation history
    let query = supabase
      .from('genyus_group_messages')
      .select(`
        id,
        message_type,
        content,
        created_at,
        user_id,
        users!inner(name, email, profile_picture_url)
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Add search filter if provided
    if (search) {
      query = query.ilike('content', `%${search}%`)
    }

    const { data: messages, error: messagesError } = await query

    if (messagesError) {
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('genyus_group_messages')
      .select('*', { count: 'exact', head: true })
      .eq('group_id', groupId)

    if (search) {
      countQuery = countQuery.ilike('content', `%${search}%`)
    }

    const { count: totalMessages } = await countQuery

    // Format messages
    const formattedMessages = (messages || []).map(msg => ({
      id: msg.id,
      type: msg.message_type,
      content: msg.content,
      timestamp: msg.created_at,
      user: {
        id: msg.user_id,
        name: msg.users.name || msg.users.email,
        profilePictureUrl: msg.users.profile_picture_url
      }
    }))

    return NextResponse.json({
      success: true,
      group: {
        id: groupId,
        name: group.name,
        createdAt: group.created_at
      },
      messages: formattedMessages,
      pagination: {
        total: totalMessages || 0,
        limit,
        offset,
        hasMore: (totalMessages || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Get group history error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
