import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ groupId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { groupId } = await params

    // Verify user is member of the group
    const { data: membership } = await supabase
      .from('genyus_group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single()

    if (!membership) {
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    // Check if user is the creator
    const { data: group } = await supabase
      .from('genyus_group_chats')
      .select('creator_user_id')
      .eq('id', groupId)
      .single()

    if (group?.creator_user_id === user.id) {
      return NextResponse.json({ 
        error: 'Group creators cannot leave. Delete the group instead.' 
      }, { status: 400 })
    }

    // Deactivate membership (soft leave)
    const { error: leaveError } = await supabase
      .from('genyus_group_members')
      .update({ is_active: false })
      .eq('id', membership.id)

    if (leaveError) {
      console.error('Leave group error:', leaveError)
      return NextResponse.json({ error: 'Failed to leave group' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Leave group error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
