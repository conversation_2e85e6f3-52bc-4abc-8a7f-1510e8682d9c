import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const inviteSchema = z.object({
  groupId: z.string().uuid(),
  inviteeUserId: z.string().uuid().optional(),
  inviteeEmail: z.string().email().optional(),
  email: z.string().email().optional(), // Support both parameter names
}).refine(data => data.inviteeUserId || data.inviteeEmail || data.email, {
  message: "Either inviteeUserId or email must be provided"
})

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { groupId, inviteeUserId, inviteeEmail, email } = inviteSchema.parse(body)

    // Normalize email parameter
    const targetEmail = inviteeEmail || email

    // Verify user is member of the group
    const { data: membership } = await supabase
      .from('genyus_group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single()

    if (!membership) {
      return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
    }

    // Get group details
    const { data: group } = await supabase
      .from('genyus_group_chats')
      .select('name, invite_code, max_members')
      .eq('id', groupId)
      .single()

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    // Check if group is at capacity
    const { count: memberCount } = await supabase
      .from('genyus_group_members')
      .select('*', { count: 'exact', head: true })
      .eq('group_id', groupId)
      .eq('is_active', true)

    if (memberCount && memberCount >= group.max_members) {
      return NextResponse.json({ error: 'Group is at maximum capacity' }, { status: 400 })
    }

    // If inviting existing user, check if they're already a member
    if (inviteeUserId) {
      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('genyus_group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', inviteeUserId)
        .eq('is_active', true)
        .single()

      if (existingMember) {
        return NextResponse.json({ error: 'User is already a member of this group' }, { status: 400 })
      }
    }

    // Create invitation
    const { data: invite, error: inviteError } = await supabase
      .from('genyus_group_invites')
      .insert({
        group_id: groupId,
        inviter_user_id: user.id,
        invitee_user_id: inviteeUserId || null,
        invitee_email: targetEmail || null,
        invite_code: group.invite_code
      })
      .select('id')
      .single()

    if (inviteError) {
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 })
    }

    // Send notification if inviting existing user
    if (inviteeUserId) {
      const { data: inviter } = await supabase
        .from('users')
        .select('name')
        .eq('id', user.id)
        .single()

      await supabase
        .from('notifications')
        .insert({
          user_id: inviteeUserId,
          type: 'group_invite',
          title: 'Group Chat Invitation',
          body: `${inviter?.name || 'Someone'} invited you to join "${group.name}" group chat`,
          data: {
            group_id: groupId,
            invite_code: group.invite_code,
            url: `/genyus/group/join/${group.invite_code}`
          }
        })
    }

    return NextResponse.json({
      success: true,
      invite: {
        id: invite.id,
        groupName: group.name,
        inviteCode: group.invite_code
      }
    })

  } catch (error) {
    console.error('Group invite error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
