import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

// GET user's groups
export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all groups user is a member of
    const { data: groups } = await supabase
      .from('genyus_group_chats')
      .select(`
        id,
        name,
        invite_code,
        creator_user_id,
        created_at,
        genyus_group_members!inner(
          joined_at
        )
      `)
      .eq('genyus_group_members.user_id', user.id)
      .eq('genyus_group_members.is_active', true)
      .order('created_at', { ascending: false })

    // Get member counts for each group
    const groupsWithCounts = await Promise.all(
      (groups || []).map(async (group) => {
        const { count } = await supabase
          .from('genyus_group_members')
          .select('*', { count: 'exact', head: true })
          .eq('group_id', group.id)
          .eq('is_active', true)

        return {
          id: group.id,
          name: group.name,
          inviteCode: group.invite_code,
          isCreator: group.creator_user_id === user.id,
          createdAt: group.created_at,
          joinedAt: group.genyus_group_members[0]?.joined_at,
          memberCount: count || 0
        }
      })
    )

    return NextResponse.json({
      success: true,
      groups: groupsWithCounts
    })

  } catch (error) {
    console.error('Get user groups error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
