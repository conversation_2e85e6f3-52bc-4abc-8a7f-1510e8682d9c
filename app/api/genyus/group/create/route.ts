import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSupabaseServerClient } from '@/lib/supabase/client'

const createGroupSchema = z.object({
  name: z.string().min(1).max(50),
})

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name } = createGroupSchema.parse(body)

    // Users can create groups regardless of token balance
    // Token usage will be checked when actually sending messages

    // Generate unique invite code
    const inviteCode = Math.random().toString(36).substring(2, 10).toUpperCase()

    // Create group chat
    const { data: group, error: groupError } = await supabase
      .from('genyus_group_chats')
      .insert({
        name,
        creator_user_id: user.id,
        invite_code: inviteCode
      })
      .select('id, name, invite_code, created_at')
      .single()

    if (groupError) {
      console.error('Group creation error:', groupError)
      return NextResponse.json({ error: 'Failed to create group' }, { status: 500 })
    }

    // Add creator as first member
    console.log('Adding creator as member:', { groupId: group.id, userId: user.id })

    const { data: memberData, error: memberError } = await supabase
      .from('genyus_group_members')
      .insert({
        group_id: group.id,
        user_id: user.id
      })
      .select('id')

    console.log('Member creation result:', { memberData, memberError })

    if (memberError) {
      console.error('Failed to add creator to group:', memberError)
      // Cleanup: delete the group if member insertion fails
      await supabase
        .from('genyus_group_chats')
        .delete()
        .eq('id', group.id)

      return NextResponse.json({ error: 'Failed to add creator to group' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      group: {
        id: group.id,
        name: group.name,
        invite_code: group.invite_code,
        created_at: group.created_at
      }
    })

  } catch (error) {
    console.error('Group creation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
