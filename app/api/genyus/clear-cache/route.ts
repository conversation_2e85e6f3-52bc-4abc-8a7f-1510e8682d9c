import { NextRequest } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import { clearCache } from "@/lib/genyus/cache";

export const runtime = "edge";

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" }
  });
}

export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return resp({ error: "Unauthorized" }, 401);
    }

    // Clear the in-memory cache
    clearCache();
    
    console.log('🧹 AI response cache cleared for user:', user.id);

    return resp({ 
      success: true, 
      message: "Cache cleared successfully" 
    });

  } catch (error) {
    console.error('Cache clear error:', error);
    return resp({ error: "Failed to clear cache" }, 500);
  }
}
