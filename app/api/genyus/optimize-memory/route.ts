import { NextRequest, NextResponse } from 'next/server';
import { optimizeAllUserMemories } from '@/lib/genyus/memory-optimization';

// This endpoint can be called by a cron job to optimize memory for all users
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron job)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET_TOKEN;
    
    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting memory optimization for all users...');
    const result = await optimizeAllUserMemories();
    
    return NextResponse.json({
      success: true,
      message: 'Memory optimization completed',
      result
    });
    
  } catch (error) {
    console.error('Memory optimization error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'Memory optimization service is running',
    timestamp: new Date().toISOString()
  });
}
