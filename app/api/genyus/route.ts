import { NextRequest } from "next/server";
import { z } from "zod";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import { processGenyusRequest } from "@/lib/genyus/orchestrator";
import { checkRateLimit, recordRequest, createRateLimitError } from "@/lib/genyus/rate-limit";

export const runtime = "edge";

const bodySchema = z.object({
  question: z.string().min(1).max(8000),
  model: z.enum(['gpt5-nano', 'deepseek']).optional().default('gpt5-nano')
});

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" }
  });
}



export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const { question, model } = bodySchema.parse(json);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return resp({ error: "Unauthorized" }, 401);
    }

    // Rate limiting
    if (!checkRateLimit(user.id)) {
      return createRateLimitError();
    }

    // Record this request
    recordRequest(user.id);

    // Check word balance
    const { data: bal, error: balError } = await supabase
      .rpc('ensure_user_has_words', { p_user_id: user.id })
      .single();

    if (balError) {
      return resp({ error: "Failed to check word balance" }, 500);
    }

    // Check if user has enough words (most responses are 50+ words)
    const minimumWordsNeeded = 50;
    if (!bal || (bal.words_remaining < minimumWordsNeeded && bal.words_remaining !== -1)) {
      return resp({
        needsUpgrade: true,
        message: "Not enough words remaining. Upgrade to continue using Genyus.",
        wordsRemaining: bal?.words_remaining || 0
      }, 402);
    }

    // Process request through orchestrator
    const response = await processGenyusRequest({
      userId: user.id,
      question,
      model, // Pass user's model choice
    });

    // Convert string stream to Uint8Array stream for Response
    const encoder = new TextEncoder();
    const transformedStream = new ReadableStream({
      async start(controller) {
        const reader = response.stream.getReader();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            controller.enqueue(encoder.encode(value));
          }
        } catch (error) {
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(transformedStream, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-store"
      }
    });

  } catch (error) {
    console.error('Genyus API error:', error);
    return resp({ error: "Internal server error" }, 500);
  }
}
