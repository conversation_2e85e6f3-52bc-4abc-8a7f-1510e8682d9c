import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { getSessionMessages } from '@/lib/genyus/conversation-sessions'

export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { sessionId } = params

    // Get messages for this session
    const messages = await getSessionMessages(user.id, sessionId)

    return NextResponse.json({
      success: true,
      messages
    })

  } catch (error) {
    console.error('Get session messages error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
