import { NextRequest } from "next/server";
import { z } from "zod";
import { createSupabaseServerClient } from "@/lib/supabase/client";

export const runtime = "edge";

const bodySchema = z.object({
  question: z.string().min(1).max(8000),
  answer: z.string().min(1).max(50000)
});

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" }
  });
}

export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const { question, answer } = bodySchema.parse(json);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return resp({ error: "Unauthorized" }, 401);
    }

    // Create a formatted diary entry for the Q&A
    const title = `OnlyGenyus Q&A: ${question.slice(0, 60)}${question.length > 60 ? '...' : ''}`;
    
    const content = `**Question:** ${question}

**OnlyGenyus Answer:**

${answer}

---

*This Q&A was generated using OnlyGenyus - our AI that provides 4x better answers by synthesizing multiple AI models.*`;

    // Create the diary entry
    const { data: entry, error: entryError } = await supabase
      .from('diary_entries')
      .insert({
        user_id: user.id,
        title: title,
        body_md: content,
        is_free: true, // OnlyGenyus shares are always free
        is_hidden: false, // Public on timeline
        allow_donations: false // No donations for AI-generated content
      })
      .select('id, title')
      .single();

    if (entryError) {
      console.error('Error creating diary entry:', entryError);
      return resp({ error: "Failed to share to timeline" }, 500);
    }

    return resp({ 
      success: true, 
      entryId: entry.id,
      title: entry.title,
      message: "Successfully shared to your timeline!" 
    });

  } catch (error) {
    console.error('Share API error:', error);
    return resp({ error: "Internal server error" }, 500);
  }
}
