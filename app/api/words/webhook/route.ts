import { NextRequest } from "next/server";
import { createClient } from "@supabase/supabase-js";
import <PERSON><PERSON> from "stripe";

export const runtime = "edge";

function createSbEdge() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const sig = req.headers.get("stripe-signature");
    
    if (!sig) {
      return new Response("Missing signature", { status: 400 });
    }

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { 
      apiVersion: "2024-06-20" 
    });

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body, 
        sig, 
        process.env.STRIPE_WEBHOOK_SECRET!
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return new Response("Invalid signature", { status: 400 });
    }

    const supabase = createSbEdge();

    if (event.type === "checkout.session.completed") {
      const session = event.data.object as Stripe.Checkout.Session;
      const userId = session.metadata?.userId;
      const words = Number(session.metadata?.words ?? 0);
      const pack = session.metadata?.pack;

      if (!userId || !words) {
        console.error('Missing metadata in session:', session.id);
        return new Response("Missing metadata", { status: 400 });
      }

      // Check if we've already processed this session (idempotency)
      const { data: existingPurchase } = await supabase
        .from('genyus_word_purchases')
        .select('id')
        .eq('stripe_session_id', session.id)
        .single();

      if (existingPurchase) {
        console.log('Session already processed:', session.id);
        return new Response("Already processed", { status: 200 });
      }

      try {
        // Insert purchase record
        const { error: purchaseError } = await supabase
          .from('genyus_word_purchases')
          .insert({
            user_id: userId,
            stripe_session_id: session.id,
            price_id: session.metadata?.priceId || 'unknown',
            words_granted: words
          });

        if (purchaseError) {
          console.error('Purchase insert error:', purchaseError);
          return new Response("Database error", { status: 500 });
        }

        // Add words to user balance
        const { error: balanceError } = await supabase
          .from('genyus_user_words')
          .upsert({
            user_id: userId,
            words_remaining: words,
            tier: pack?.toLowerCase() || 'starter'
          }, {
            onConflict: 'user_id',
            ignoreDuplicates: false
          });

        if (balanceError) {
          // If upsert failed, try to increment existing balance
          const { error: incrementError } = await supabase.rpc('increment_user_words', {
            p_user_id: userId,
            p_words: words
          });

          if (incrementError) {
            console.error('Balance update error:', incrementError);
            return new Response("Balance update failed", { status: 500 });
          }
        }

        console.log(`Successfully processed purchase for user ${userId}: ${words} words`);

      } catch (error) {
        console.error('Transaction error:', error);
        return new Response("Transaction failed", { status: 500 });
      }
    }

    return new Response("OK", { status: 200 });

  } catch (error) {
    console.error('Webhook error:', error);
    return new Response("Internal error", { status: 500 });
  }
}
