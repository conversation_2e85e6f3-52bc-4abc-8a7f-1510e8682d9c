import { NextRequest } from "next/server";
import { z } from "zod";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import <PERSON><PERSON> from "stripe";
import { WORD_PACKS } from "@/lib/genyus/config";

export const runtime = "edge";

const bodySchema = z.object({
  priceId: z.string().min(1),
  returnTo: z.string().optional()
});

function resp(obj: any, status = 200) {
  return new Response(JSON.stringify(obj), {
    status,
    headers: { "Content-Type": "application/json" }
  });
}

export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const { priceId } = bodySchema.parse(json);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return resp({ error: "Unauthorized" }, 401);
    }

    // Find the word pack for this price ID
    const wordPack = Object.values(WORD_PACKS).find(pack => pack.priceId === priceId);
    if (!wordPack) {
      return resp({ error: "Invalid price ID" }, 400);
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { 
      apiVersion: "2024-06-20" 
    });

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      mode: "payment",
      payment_method_types: ["card"],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${process.env.APP_ORIGIN || 'https://onlydiary.app'}/genyus?purchase=success`,
      cancel_url: `${process.env.APP_ORIGIN || 'https://onlydiary.app'}/genyus?purchase=cancelled`,
      metadata: {
        userId: user.id,
        words: wordPack.words.toString(),
        pack: wordPack.label
      },
    });

    return resp({ 
      sessionId: session.id,
      url: session.url 
    });

  } catch (error) {
    console.error('Checkout error:', error);
    return resp({ error: "Failed to create checkout session" }, 500);
  }
}
