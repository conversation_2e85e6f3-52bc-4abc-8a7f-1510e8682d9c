'use client'

import { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'

interface GroupJoinPageProps {
  params: Promise<{ inviteCode: string }>
}

export default function GroupJoinPage({ params }: GroupJoinPageProps) {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [joining, setJoining] = useState(false)
  const [message, setMessage] = useState('')
  const [groupInfo, setGroupInfo] = useState<any>(null)

  const router = useRouter()
  const supabase = createSupabaseClient()
  const { inviteCode } = use(params)

  useEffect(() => {
    console.log('JOIN PAGE: Component mounted with invite code:', inviteCode)
    // Track invite link click
    trackInviteClick()
    checkAuthAndGroup()
  }, [inviteCode])

  const trackInviteClick = async () => {
    try {
      // Track the invite link click
      await supabase.from('invite_link_clicks').insert({
        invite_code: inviteCode,
        user_agent: navigator.userAgent,
        referrer: document.referrer || null
      })

      // Update invite status to clicked
      await supabase
        .from('genyus_group_invites')
        .update({
          status: 'clicked',
          clicked_at: new Date().toISOString()
        })
        .eq('invite_code', inviteCode)
        .eq('status', 'pending')
    } catch (error) {
      console.error('Error tracking invite click:', error)
    }
  }

  const checkAuthAndGroup = async () => {
    try {
      console.log('JOIN PAGE: Starting auth check...')
      // Check authentication
      const { data: { user } } = await supabase.auth.getUser()

      console.log('JOIN PAGE: Auth result:', { user: user?.id, email: user?.email })

      if (!user) {
        console.log('JOIN PAGE: No user, redirecting to login')
        router.push(`/login?next=${encodeURIComponent(`/genyus/group/join/${inviteCode}`)}`)
        return
      }

      console.log('JOIN PAGE: User authenticated, setting user state')
      setUser(user)

      // Debug: Log the invite code we're looking for
      console.log('JOIN PAGE: Looking for invite code:', inviteCode)

      // Get group info by invite code (simplified query)
      const { data: group, error: groupError } = await supabase
        .from('genyus_group_chats')
        .select('id, name, creator_user_id, created_at, invite_code, is_active')
        .eq('invite_code', inviteCode)
        .eq('is_active', true)
        .single()

      // Debug: Log the query result
      console.log('JOIN PAGE: Group query result:', { group, groupError })

      // Also check all groups to see what invite codes exist
      const { data: allGroups } = await supabase
        .from('genyus_group_chats')
        .select('id, name, invite_code, is_active')
        .eq('is_active', true)

      console.log('JOIN PAGE: All active groups:', allGroups)

      if (!group) {
        // Try to find the group without the is_active filter to see if it exists but is inactive
        const { data: inactiveGroup } = await supabase
          .from('genyus_group_chats')
          .select('id, name, is_active, invite_code')
          .eq('invite_code', inviteCode)
          .single()

        console.log('Inactive group check:', inactiveGroup)

        if (inactiveGroup && !inactiveGroup.is_active) {
          setMessage('This group chat has been deactivated')
        } else {
          setMessage('Invalid or expired invite code')
        }
        setLoading(false)
        return
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('genyus_group_members')
        .select('id, is_active')
        .eq('group_id', group.id)
        .eq('user_id', user.id)
        .single()

      if (existingMember?.is_active) {
        router.push(`/genyus?group=${group.id}`)
        return
      }

      // Get member count and details
      const { data: members, count: memberCount } = await supabase
        .from('genyus_group_members')
        .select(`
          user_id,
          joined_at,
          users!inner(
            id,
            name,
            email
          )
        `, { count: 'exact' })
        .eq('group_id', group.id)
        .eq('is_active', true)

      console.log('JOIN PAGE: Members query result:', { members, memberCount })

      // Get member names for display
      const memberNames = members?.map(m => m.users?.name || m.users?.email?.split('@')[0] || 'Unknown') || []

      setGroupInfo({
        id: group.id,
        name: group.name,
        memberCount: memberCount || 0,
        members: memberNames
      })

    } catch (error) {
      console.error('Error checking group:', error)
      setMessage('Error loading group information')
    } finally {
      setLoading(false)
    }
  }

  const joinGroup = async () => {
    if (!user || !groupInfo) return

    setJoining(true)
    setMessage('')

    try {
      const response = await fetch('/api/genyus/group/join', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ inviteCode })
      })

      const data = await response.json()

      if (response.ok) {
        router.push(`/genyus?group=${groupInfo.id}`)
      } else {
        setMessage(data.error || 'Failed to join group')
        
        if (data.needsUpgrade) {
          // Show upgrade prompt with return URL
          setTimeout(() => {
            router.push(`/genyus?upgrade=true&returnTo=${encodeURIComponent(`/genyus/group/join/${inviteCode}`)}`)
          }, 2000)
        }
      }
    } catch (error) {
      setMessage('Failed to join group')
    } finally {
      setJoining(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading group information...</p>
        </div>
      </div>
    )
  }

  if (!groupInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-2xl p-8 text-center shadow-lg">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Invalid Invite</h1>
            <p className="text-gray-600 mb-6">{message}</p>
            <Link
              href="/genyus"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to OnlyGenyus
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2m-2-4H9m12 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2h10z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Join Group Chat</h1>
            <p className="text-gray-600">You've been invited to join a group chat on OnlyGenyus</p>
          </div>

          <div className="bg-gray-50 rounded-xl p-4 mb-6">
            <h2 className="font-semibold text-gray-900 mb-2">{groupInfo.name}</h2>
            <p className="text-sm text-gray-600 mb-3">
              {groupInfo.memberCount} member{groupInfo.memberCount !== 1 ? 's' : ''}
            </p>
            {groupInfo.members.length > 0 && (
              <div className="text-sm text-gray-500">
                <span className="font-medium">Members:</span> {groupInfo.members.slice(0, 3).join(', ')}
                {groupInfo.memberCount > 3 && ` and ${groupInfo.memberCount - 3} more`}
              </div>
            )}
            {groupInfo.memberCount === 0 && (
              <div className="text-sm text-gray-500">
                You'll be the first member to join this group!
              </div>
            )}
          </div>

          {message && (
            <div className={`p-3 rounded-lg text-sm mb-4 ${
              message.includes('success') 
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {message}
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={joinGroup}
              disabled={joining}
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {joining ? 'Joining...' : 'Join Group Chat'}
            </button>
            
            <Link
              href="/genyus"
              className="block w-full text-center py-3 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Go to OnlyGenyus
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
