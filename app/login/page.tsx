"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"
import { LinkButton } from "@/components/ui/link-button"
import { GoogleButton } from "@/components/ui/button"
import { useRouter } from "next/navigation" // Import useRouter

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState("")
  const [error, setError] = useState("")
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)


  const isInAppBrowser = typeof navigator !== 'undefined' && /FBAN|FBAV|Instagram|Line|Wechat|Weibo|TikTok/i.test(navigator.userAgent)
  const [notice, setNotice] = useState<string>("")

  const attemptOpenInBrowser = async () => {
    try {
      const url = window.location.href
      const ua = navigator.userAgent || ''
      // Best effort: Android Chrome intent
      if (/Android/i.test(ua)) {
        const target = 'intent://' + url.replace(/^https?:\/\//, '') + '#Intent;scheme=https;package=com.android.chrome;end'
        // Try to open Chrome
        window.location.href = target
        // Fallback: new tab
        setTimeout(() => {
          window.open(url, '_blank', 'noopener,noreferrer')
        }, 400)
      } else {
        // iOS: no public scheme to force Safari; open new tab and show guidance
        const win = window.open(url, '_blank', 'noopener,noreferrer')
        if (!win) {
          setNotice('If this page stays inside the app, use ••• or share icon → Open in Browser')
        }
      }
    } catch {
      setNotice('If this page stays inside the app, use ••• or share icon → Open in Browser')
    }
  }

  const shareCurrent = async () => {
    try {
      const url = window.location.href
      if (navigator.share) {
        await navigator.share({ title: 'OnlyDiary', text: 'Open this in your device browser', url })
      } else {
        await navigator.clipboard.writeText(url)
        setNotice('Link copied — paste it into Safari/Chrome')
      }
    } catch {}
  }

  const copyCurrent = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setNotice('Link copied — open your browser and paste')
    } catch {}
  }

  const supabase = createSupabaseClient()
  const router = useRouter() // Initialize useRouter

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)
    setLoadingMessage("Signing in...")

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password.trim(),
      })

      if (error) {
        setError(error.message || "Login failed. Please try again.")
        setLoading(false)
        setLoadingMessage("")
        return
      }

      if (data?.session?.user) {
        setLoadingMessage("Loading your profile...")

        // Check if there's a specific destination to redirect to
        const params = new URLSearchParams(window.location.search)
        const nextDest = params.get('next')

        if (nextDest) {
          setLoadingMessage("Redirecting...")
          router.replace(nextDest)
          return
        }

        // Get user profile and determine optimal destination
        const { data: profile } = await supabase
          .from('users')
          .select('role')
          .eq('id', data.session.user.id)
          .single()

        // Admins always go to dashboard
        if (profile?.role === 'admin') {
          setLoadingMessage("Redirecting to admin dashboard...")
          router.replace('/dashboard')
          return
        }

        // For regular users, check if they're following anyone
        const { data: follows } = await supabase
          .from('follows')
          .select('id')
          .eq('follower_id', data.session.user.id)
          .limit(1)

        if (!follows || follows.length === 0) {
          setLoadingMessage("Welcome back! Setting up your dashboard...")
          router.replace('/dashboard')
        } else {
          setLoadingMessage("Welcome back! Loading your timeline...")
          router.replace('/timeline')
        }

        // Keep loading active during redirect - don't set to false
        // The page will unmount when navigation completes
      } else {
        setError("Login failed. Please try again.")
        setLoading(false)
        setLoadingMessage("")
      }
    } catch {
      setError("An unexpected error occurred. Please try again.")
      setLoading(false)
      setLoadingMessage("")
    }
  }

  const handleGoogleSignIn = async () => {
    setError("")
    setIsGoogleLoading(true)

    try {
      const params = new URLSearchParams(window.location.search)
      const nextDest = params.get('next') || '/timeline'
      const origin = window.location.origin // Always use runtime origin (handles ngrok/localhost/prod)
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${origin}/auth/callback?next=${encodeURIComponent(nextDest)}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        }
      })

      if (error) {
        setError('Failed to sign in with Google. Please try again.')
        setIsGoogleLoading(false)
      }
    } catch {
      setError('An unexpected error occurred with Google sign in')
      setIsGoogleLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6 relative">
      {/* Full-page loading overlay for redirect */}
      {loading && loadingMessage.includes("Redirecting") && (
        <div className="fixed inset-0 bg-white/90 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-gray-300 border-t-gray-800 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-800 font-medium text-lg">{loadingMessage}</p>
            <p className="text-gray-600 text-sm mt-2">This may take a few seconds...</p>
          </div>
        </div>
      )}

      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600">
            OnlyDiary
          </Link>
          <h1 className="text-2xl font-serif mt-4 text-gray-800">Welcome Back</h1>
          <p className="text-gray-600 font-serif mt-2">
            Sign in to your account to access your subscriptions
          </p>
        </div>

        {/* In-app browser interstitial with best-effort escape tools */}
        {isInAppBrowser && (
          <div className="mb-3 rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-900 text-left">
            <div className="mb-2">Some social apps restrict Google sign-in inside their in-app browser.</div>
            <div className="flex gap-2 flex-wrap">
              <button onClick={attemptOpenInBrowser} className="px-3 py-1.5 rounded bg-gray-900 text-white text-xs">Open in Browser</button>
              <button onClick={shareCurrent} className="px-3 py-1.5 rounded bg-white text-gray-800 border text-xs">Share</button>
              <button onClick={copyCurrent} className="px-3 py-1.5 rounded bg-white text-gray-800 border text-xs">Copy Link</button>
            </div>
            {notice && <div className="mt-2 text-xs text-yellow-900/90">{notice}</div>}
          </div>
        )}

        {/* Google Sign In Button */}
        <GoogleButton
          onClick={handleGoogleSignIn}
          isLoading={isGoogleLoading}
          disabled={loading}
        >
          Continue with Google
        </GoogleButton>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with email</span>
          </div>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent placeholder:text-gray-600 md:placeholder:text-gray-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent placeholder:text-gray-600 md:placeholder:text-gray-500"
              placeholder="Your password"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gray-800 text-white hover:bg-gray-700 px-4 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {loadingMessage || "Signing in..."}
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>



        <div className="mt-6 text-center">
          <p className="text-gray-600 font-serif mb-4">
            Don&apos;t have an account?
          </p>
          <LinkButton href="/register" variant="outline" className="w-full">
            Create Account
          </LinkButton>
        </div>

        <div className="mt-6 text-center">
          <LinkButton href="/forgot-password" variant="ghost" className="text-gray-600 hover:text-gray-800 text-sm p-0 h-auto">
            Forgot your password?
          </LinkButton>
        </div>
      </div>
    </div>
  )
}
