'use client'

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface OnboardingState {
  shouldShow: boolean
  source: 'group_chat' | 'signup' | 'manual'
  hasCompletedOnboarding: boolean
  lastGroupChatActivity: Date | null
  isNewUser: boolean
}

export function useSocialMedia2Onboarding(userId?: string) {
  const [state, setState] = useState<OnboardingState>({
    shouldShow: false,
    source: 'signup',
    hasCompletedOnboarding: false,
    lastGroupChatActivity: null,
    isNewUser: false
  })
  
  const [showModal, setShowModal] = useState(false)
  const supabase = createSupabaseClient()

  // Check if user needs onboarding
  const checkOnboardingStatus = useCallback(async () => {
    if (!userId) return

    try {
      // Check if user has completed onboarding
      const { data: onboardingData } = await supabase
        .from('user_onboarding_actions')
        .select('action, completed_at, source')
        .eq('user_id', userId)
        .eq('action', 'onboarding_completed')
        .single()

      const hasCompleted = !!onboardingData

      // Check user creation date (new user = created in last 24 hours)
      const { data: userData } = await supabase
        .from('users')
        .select('created_at')
        .eq('id', userId)
        .single()

      const isNewUser = userData ? 
        Date.now() - new Date(userData.created_at).getTime() < 24 * 60 * 60 * 1000 : false

      // Check recent group chat activity
      const { data: groupActivity } = await supabase
        .from('genyus_group_messages')
        .select('created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      const lastGroupChatActivity = groupActivity ? new Date(groupActivity.created_at) : null

      setState(prev => ({
        ...prev,
        hasCompletedOnboarding: hasCompleted,
        isNewUser,
        lastGroupChatActivity
      }))

      return {
        hasCompleted,
        isNewUser,
        lastGroupChatActivity
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error)
      return null
    }
  }, [userId, supabase])

  // Trigger onboarding for group chat guests
  const triggerGroupChatOnboarding = useCallback(() => {
    if (state.hasCompletedOnboarding) return

    setState(prev => ({
      ...prev,
      shouldShow: true,
      source: 'group_chat'
    }))
    
    setShowModal(true)
  }, [state.hasCompletedOnboarding])

  // Trigger onboarding for new signups
  const triggerSignupOnboarding = useCallback(() => {
    if (state.hasCompletedOnboarding) return

    setState(prev => ({
      ...prev,
      shouldShow: true,
      source: 'signup'
    }))
    
    setShowModal(true)
  }, [state.hasCompletedOnboarding])

  // Manual trigger
  const triggerManualOnboarding = useCallback(() => {
    setState(prev => ({
      ...prev,
      shouldShow: true,
      source: 'manual'
    }))
    
    setShowModal(true)
  }, [])

  // Smart detection for group chat guests
  useEffect(() => {
    if (!userId || state.hasCompletedOnboarding) return

    const detectGroupChatGuest = async () => {
      const status = await checkOnboardingStatus()
      if (!status) return

      const { isNewUser, lastGroupChatActivity } = status

      // If user is new AND has recent group chat activity, they're likely a group chat guest
      if (isNewUser && lastGroupChatActivity) {
        const timeSinceLastActivity = Date.now() - lastGroupChatActivity.getTime()
        
        // If last group chat was within 10 minutes and no recent activity, show onboarding
        if (timeSinceLastActivity > 5 * 60 * 1000 && timeSinceLastActivity < 10 * 60 * 1000) {
          triggerGroupChatOnboarding()
        }
      }
      // If user is new but no group chat activity, they're a regular signup
      else if (isNewUser && !lastGroupChatActivity) {
        triggerSignupOnboarding()
      }
    }

    detectGroupChatGuest()
  }, [userId, checkOnboardingStatus, triggerGroupChatOnboarding, triggerSignupOnboarding, state.hasCompletedOnboarding])

  // Listen for group chat inactivity
  useEffect(() => {
    if (!userId || state.hasCompletedOnboarding) return

    let inactivityTimer: NodeJS.Timeout

    const handleGroupChatInactivity = () => {
      // Clear existing timer
      if (inactivityTimer) clearTimeout(inactivityTimer)

      // Set new timer for 5 minutes of inactivity
      inactivityTimer = setTimeout(() => {
        if (state.isNewUser && !state.hasCompletedOnboarding) {
          triggerGroupChatOnboarding()
        }
      }, 5 * 60 * 1000) // 5 minutes
    }

    // Listen for group chat activity
    const handleActivity = () => {
      handleGroupChatInactivity()
    }

    // Listen for custom events from group chat
    window.addEventListener('groupChatActivity', handleActivity)
    window.addEventListener('groupChatMessage', handleActivity)

    return () => {
      if (inactivityTimer) clearTimeout(inactivityTimer)
      window.removeEventListener('groupChatActivity', handleActivity)
      window.removeEventListener('groupChatMessage', handleActivity)
    }
  }, [userId, state.isNewUser, state.hasCompletedOnboarding, triggerGroupChatOnboarding])

  const closeModal = useCallback(() => {
    setShowModal(false)
    setState(prev => ({
      ...prev,
      shouldShow: false
    }))
  }, [])

  const markCompleted = useCallback(async () => {
    if (!userId) return

    try {
      await supabase.from('user_onboarding_actions').insert({
        user_id: userId,
        action: 'onboarding_completed',
        source: state.source,
        completed_at: new Date().toISOString()
      })

      setState(prev => ({
        ...prev,
        hasCompletedOnboarding: true,
        shouldShow: false
      }))
      
      setShowModal(false)
    } catch (error) {
      console.error('Error marking onboarding complete:', error)
    }
  }, [userId, state.source, supabase])

  return {
    // State
    shouldShow: showModal,
    source: state.source,
    hasCompletedOnboarding: state.hasCompletedOnboarding,
    isNewUser: state.isNewUser,
    
    // Actions
    triggerGroupChatOnboarding,
    triggerSignupOnboarding,
    triggerManualOnboarding,
    closeModal,
    markCompleted,
    checkOnboardingStatus,
    
    // Utilities
    state
  }
}

// Helper function to emit group chat activity events
export function emitGroupChatActivity() {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('groupChatActivity'))
  }
}

export function emitGroupChatMessage() {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('groupChatMessage'))
  }
}
