import { createClient } from "@supabase/supabase-js";
import { memoryManager } from "./memory-management";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface ContextualMemoryResult {
  relevantMemories: Array<{
    content: string;
    relevanceScore: number;
    memoryType: 'immediate' | 'short_term' | 'long_term' | 'permanent';
    timestamp?: Date;
    importance: number;
  }>;
  contextSummary: string;
  relationshipContext: string;
  totalTokensUsed: number;
}

export class ContextualMemoryRetriever {
  private supabase = createSupabaseServiceClient();

  async getSmartContextualMemory(
    userId: string,
    currentQuestion: string,
    maxTokens: number = 2500  // Conservative limit for cost efficiency
  ): Promise<ContextualMemoryResult> {
    
    // 1. Analyze the current question for context clues
    const questionAnalysis = this.analyzeQuestionForContext(currentQuestion);
    
    // 2. Get hierarchical memory using the intelligent manager
    const memoryLayers = await memoryManager.getContextualMemory(userId, currentQuestion, maxTokens);
    
    // 3. Score and rank all memories by relevance to current question
    const scoredMemories = await this.scoreMemoriesByRelevance(
      memoryLayers,
      questionAnalysis,
      currentQuestion
    );
    
    // 4. Select optimal memory mix within token budget
    const selectedMemories = this.selectOptimalMemoryMix(scoredMemories, maxTokens);
    
    // 5. Generate context summary and relationship context
    const contextSummary = this.generateContextSummary(selectedMemories, questionAnalysis);
    const relationshipContext = this.generateRelationshipContext(memoryLayers.permanent);
    
    return {
      relevantMemories: selectedMemories,
      contextSummary,
      relationshipContext,
      totalTokensUsed: this.estimateTokens(JSON.stringify(selectedMemories))
    };
  }

  private analyzeQuestionForContext(question: string): {
    topics: string[];
    timeReferences: string[];
    intentType: 'recall' | 'continuation' | 'new_topic' | 'clarification' | 'feedback';
    emotionalTone: string;
    complexityLevel: number;
    requiresPersonalContext: boolean;
    requiresHistoricalContext: boolean;
  } {
    const lowerQuestion = question.toLowerCase();
    
    // Extract topics using enhanced keyword matching
    const topics = this.extractTopics(question);
    
    // Extract time references
    const timeReferences = this.extractTimeReferences(question);
    
    // Determine intent type
    let intentType: 'recall' | 'continuation' | 'new_topic' | 'clarification' | 'feedback' = 'new_topic';
    
    if (lowerQuestion.includes('remember') || lowerQuestion.includes('mentioned') || lowerQuestion.includes('said')) {
      intentType = 'recall';
    } else if (lowerQuestion.includes('continue') || lowerQuestion.includes('follow up') || lowerQuestion.includes('also')) {
      intentType = 'continuation';
    } else if (lowerQuestion.includes('what do you mean') || lowerQuestion.includes('clarify') || lowerQuestion.includes('explain')) {
      intentType = 'clarification';
    } else if (lowerQuestion.includes('thank') || lowerQuestion.includes('great') || lowerQuestion.includes('helpful')) {
      intentType = 'feedback';
    }
    
    // Detect emotional tone
    const emotionalTone = this.detectEmotionalTone(question);
    
    // Estimate complexity
    const complexityLevel = Math.min(10, Math.max(1, Math.floor(question.length / 50) + 1));
    
    // Check if personal or historical context is needed
    const requiresPersonalContext = lowerQuestion.includes('my') || lowerQuestion.includes('i') || intentType === 'recall';
    const requiresHistoricalContext = timeReferences.length > 0 || intentType === 'continuation';
    
    return {
      topics,
      timeReferences,
      intentType,
      emotionalTone,
      complexityLevel,
      requiresPersonalContext,
      requiresHistoricalContext
    };
  }

  private async scoreMemoriesByRelevance(
    memoryLayers: any,
    questionAnalysis: any,
    currentQuestion: string
  ): Promise<Array<{
    content: string;
    relevanceScore: number;
    memoryType: 'immediate' | 'short_term' | 'long_term' | 'permanent';
    timestamp?: Date;
    importance: number;
    originalData: any;
  }>> {
    const scoredMemories: any[] = [];
    
    // Score immediate memories (recent conversations)
    memoryLayers.immediate.forEach((memory: any) => {
      const relevanceScore = this.calculateRelevanceScore(
        memory.userMessage + ' ' + memory.assistantResponse,
        memory.topics,
        questionAnalysis,
        currentQuestion
      );
      
      scoredMemories.push({
        content: `Q: ${memory.userMessage}\nA: ${memory.assistantResponse}`,
        relevanceScore: relevanceScore + 0.3, // Boost recent memories
        memoryType: 'immediate',
        timestamp: memory.timestamp,
        importance: memory.importance,
        originalData: memory
      });
    });
    
    // Score short-term summaries
    memoryLayers.shortTerm.forEach((summary: any) => {
      const relevanceScore = this.calculateRelevanceScore(
        summary.compressed_content,
        summary.topic_focus,
        questionAnalysis,
        currentQuestion
      );
      
      scoredMemories.push({
        content: `${summary.timeframe}: ${summary.compressed_content}`,
        relevanceScore,
        memoryType: 'short_term',
        importance: 6, // Medium importance
        originalData: summary
      });
    });
    
    // Score long-term insights
    memoryLayers.longTerm.forEach((insight: any) => {
      const relevanceScore = this.calculateRelevanceScore(
        insight.content,
        [insight.type],
        questionAnalysis,
        currentQuestion
      );
      
      scoredMemories.push({
        content: `${insight.type}: ${insight.content}`,
        relevanceScore: relevanceScore + (insight.confidence * 0.2), // Boost high-confidence insights
        memoryType: 'long_term',
        importance: insight.importance,
        originalData: insight
      });
    });
    
    // Always include permanent memory with high relevance
    const permanentContent = this.formatPermanentMemory(memoryLayers.permanent);
    scoredMemories.push({
      content: permanentContent,
      relevanceScore: 0.9, // Always highly relevant
      memoryType: 'permanent',
      importance: 10,
      originalData: memoryLayers.permanent
    });
    
    return scoredMemories.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  private calculateRelevanceScore(
    memoryContent: string,
    memoryTopics: string[],
    questionAnalysis: any,
    currentQuestion: string
  ): number {
    let score = 0;
    
    // Topic overlap
    const topicOverlap = questionAnalysis.topics.filter((topic: string) => 
      memoryTopics.includes(topic) || memoryContent.toLowerCase().includes(topic.toLowerCase())
    ).length;
    score += topicOverlap * 0.3;
    
    // Keyword similarity (simple approach)
    const questionWords = currentQuestion.toLowerCase().split(/\s+/);
    const memoryWords = memoryContent.toLowerCase().split(/\s+/);
    const commonWords = questionWords.filter(word => 
      word.length > 3 && memoryWords.includes(word)
    ).length;
    score += (commonWords / questionWords.length) * 0.4;
    
    // Intent-based scoring
    if (questionAnalysis.intentType === 'recall' && memoryContent.includes('remember')) {
      score += 0.5;
    }
    
    if (questionAnalysis.intentType === 'continuation' && memoryContent.includes('follow')) {
      score += 0.4;
    }
    
    // Time relevance
    if (questionAnalysis.timeReferences.length > 0) {
      const hasTimeMatch = questionAnalysis.timeReferences.some((timeRef: string) =>
        memoryContent.toLowerCase().includes(timeRef.toLowerCase())
      );
      if (hasTimeMatch) score += 0.3;
    }
    
    return Math.min(1, score);
  }

  private selectOptimalMemoryMix(
    scoredMemories: any[],
    maxTokens: number
  ): any[] {
    const selected: any[] = [];
    let currentTokens = 0;
    
    // Always include permanent memory first
    const permanentMemory = scoredMemories.find(m => m.memoryType === 'permanent');
    if (permanentMemory) {
      selected.push(permanentMemory);
      currentTokens += this.estimateTokens(permanentMemory.content);
    }
    
    // Add memories by relevance score, ensuring diversity
    const remainingMemories = scoredMemories.filter(m => m.memoryType !== 'permanent');
    const memoryTypeCounts = { immediate: 0, short_term: 0, long_term: 0 };
    
    for (const memory of remainingMemories) {
      const memoryTokens = this.estimateTokens(memory.content);
      
      // Check if we have room and haven't exceeded type limits
      if (currentTokens + memoryTokens < maxTokens * 0.9) { // Leave 10% buffer
        const typeCount = memoryTypeCounts[memory.memoryType as keyof typeof memoryTypeCounts];
        
        // Conservative limits for cost efficiency - still provides excellent context
        const maxPerType = {
          immediate: 3,  // 3 most relevant recent conversations
          short_term: 2, // 2 relevant summaries
          long_term: 3   // 3 key insights
        };
        
        if (typeCount < maxPerType[memory.memoryType as keyof typeof maxPerType]) {
          selected.push(memory);
          currentTokens += memoryTokens;
          memoryTypeCounts[memory.memoryType as keyof typeof memoryTypeCounts]++;
        }
      }
    }
    
    return selected;
  }

  private generateContextSummary(selectedMemories: any[], questionAnalysis: any): string {
    const memoryTypes = selectedMemories.map(m => m.memoryType);
    const hasImmediate = memoryTypes.includes('immediate');
    const hasShortTerm = memoryTypes.includes('short_term');
    const hasLongTerm = memoryTypes.includes('long_term');
    
    let summary = "Context: ";
    
    if (hasImmediate) {
      summary += "Recent conversations available. ";
    }
    
    if (hasShortTerm) {
      summary += "Historical patterns from past weeks included. ";
    }
    
    if (hasLongTerm) {
      summary += "Long-term insights about user preferences and patterns available. ";
    }
    
    if (questionAnalysis.intentType === 'recall') {
      summary += "User is asking about something from our previous conversations. ";
    }
    
    return summary.trim();
  }

  private generateRelationshipContext(permanentMemory: any): string {
    const stage = permanentMemory.essential_context?.relationship_stage || 'new';
    const trustLevel = permanentMemory.essential_context?.trust_level || 1;
    const interactionCount = permanentMemory.essential_context?.interaction_count || 0;
    
    let context = `Relationship: ${stage} (trust level ${trustLevel}/10, ${interactionCount} interactions). `;
    
    if (stage === 'new') {
      context += "Building initial rapport and understanding user's communication style.";
    } else if (stage === 'developing') {
      context += "Established basic trust, learning user's preferences and patterns.";
    } else if (stage === 'established') {
      context += "Strong working relationship with good understanding of user's needs.";
    } else if (stage === 'trusted') {
      context += "Deep, trusted partnership with comprehensive understanding of user.";
    }
    
    return context;
  }

  private extractTopics(question: string): string[] {
    const topics: string[] = [];
    const topicKeywords = [
      'work', 'business', 'career', 'job', 'project', 'team', 'management',
      'health', 'fitness', 'wellness', 'medical', 'exercise', 'diet',
      'family', 'relationship', 'friends', 'social', 'personal',
      'technology', 'programming', 'software', 'AI', 'computer', 'tech',
      'learning', 'education', 'study', 'course', 'skill', 'knowledge',
      'creative', 'art', 'design', 'writing', 'music', 'photography',
      'finance', 'money', 'investment', 'budget', 'financial', 'economy',
      'travel', 'vacation', 'trip', 'adventure', 'explore',
      'home', 'house', 'apartment', 'living', 'space', 'organization'
    ];
    
    const lowerQuestion = question.toLowerCase();
    topicKeywords.forEach(topic => {
      if (lowerQuestion.includes(topic)) {
        topics.push(topic);
      }
    });
    
    return topics;
  }

  private extractTimeReferences(question: string): string[] {
    const timeRefs: string[] = [];
    const timeKeywords = [
      'yesterday', 'today', 'tomorrow', 'last week', 'next week',
      'last month', 'next month', 'recently', 'before', 'previously',
      'earlier', 'later', 'soon', 'now', 'currently', 'past', 'future'
    ];
    
    const lowerQuestion = question.toLowerCase();
    timeKeywords.forEach(time => {
      if (lowerQuestion.includes(time)) {
        timeRefs.push(time);
      }
    });
    
    return timeRefs;
  }

  private detectEmotionalTone(question: string): string {
    const lowerQuestion = question.toLowerCase();
    
    if (lowerQuestion.includes('excited') || lowerQuestion.includes('amazing') || lowerQuestion.includes('love')) {
      return 'excited';
    } else if (lowerQuestion.includes('frustrated') || lowerQuestion.includes('difficult') || lowerQuestion.includes('problem')) {
      return 'frustrated';
    } else if (lowerQuestion.includes('curious') || lowerQuestion.includes('wondering') || lowerQuestion.includes('interested')) {
      return 'curious';
    } else if (lowerQuestion.includes('worried') || lowerQuestion.includes('concerned') || lowerQuestion.includes('anxious')) {
      return 'concerned';
    } else if (lowerQuestion.includes('grateful') || lowerQuestion.includes('thank') || lowerQuestion.includes('appreciate')) {
      return 'grateful';
    }
    
    return 'neutral';
  }

  private formatPermanentMemory(permanentMemory: any): string {
    const context = permanentMemory.essential_context || {};
    
    let formatted = `User Profile: `;
    formatted += `Communication style: ${JSON.stringify(context.communication_style || {})}. `;
    formatted += `Interests: ${(context.core_interests || []).join(', ')}. `;
    formatted += `Goals: ${(context.primary_goals || []).join(', ')}. `;
    formatted += `Relationship stage: ${context.relationship_stage || 'new'}. `;
    
    if (permanentMemory.never_forget && permanentMemory.never_forget.length > 0) {
      formatted += `Important to remember: ${permanentMemory.never_forget.join(', ')}. `;
    }
    
    return formatted;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}

export const contextualRetriever = new ContextualMemoryRetriever();
