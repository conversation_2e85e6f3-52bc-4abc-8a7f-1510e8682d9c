// Smart Response Timing System for OnlyGenyus Group Chats
// Manages when AI should respond vs wait for more user input

export interface ResponseTiming {
  shouldWait: boolean
  waitingFor: 'specific_user' | 'any_user' | 'none'
  reason: string
  responseType: 'immediate' | 'wait_for_answer' | 'gentle_nudge'
  targetUserId?: string
  maxWaitTime?: number // milliseconds, for fallback
}

export class ResponseTimingManager {
  private static pendingResponses = new Map<string, NodeJS.Timeout>()
  
  static async scheduleResponse(
    groupId: string,
    timing: ResponseTiming,
    responseCallback: () => Promise<void>
  ): Promise<void> {
    // Clear any existing pending response for this group
    this.clearPendingResponse(groupId)

    // If we shouldn't wait, respond immediately
    if (!timing.shouldWait) {
      await responseCallback()
      return
    }

    // Derive a sensible delay if one isn't explicitly provided
    const DEFAULT_WAIT_FOR_ANSWER_MS = 30_000 // 30s
    const DEFAULT_GENTLE_NUDGE_MS = 10_000 // 10s

    const delayMs = typeof timing.maxWaitTime === 'number'
      ? Math.min(timing.maxWaitTime, DEFAULT_WAIT_FOR_ANSWER_MS)
      : (timing.responseType === 'wait_for_answer'
          ? DEFAULT_WAIT_FOR_ANSWER_MS
          : DEFAULT_GENTLE_NUDGE_MS)

    // Schedule delayed response
    const timeout = setTimeout(async () => {
      try {
        await responseCallback()
        this.pendingResponses.delete(groupId)
      } catch (error) {
        console.error('Delayed response error:', error)
        this.pendingResponses.delete(groupId)
      }
    }, delayMs)

    this.pendingResponses.set(groupId, timeout)
  }
  
  static clearPendingResponse(groupId: string): void {
    const existing = this.pendingResponses.get(groupId)
    if (existing) {
      clearTimeout(existing)
      this.pendingResponses.delete(groupId)
    }
  }
  
  static hasPendingResponse(groupId: string): boolean {
    return this.pendingResponses.has(groupId)
  }
  
  // Cancel pending response when new user message arrives
  static onNewUserMessage(groupId: string): void {
    this.clearPendingResponse(groupId)
  }
}

// Enhanced conversation state tracking
export interface ConversationStateTracker {
  groupId: string
  lastActivity: Date
  lastAIResponse: Date | null
  awaitingUserInput: boolean
  questionsPending: number
  activeUsers: Set<string>
  conversationPhase: 'opening' | 'active' | 'waiting' | 'concluding'
}

export class ConversationStateManager {
  private static states = new Map<string, ConversationStateTracker>()
  
  static updateState(groupId: string, update: Partial<ConversationStateTracker>): void {
    const current = this.states.get(groupId) || {
      groupId,
      lastActivity: new Date(),
      lastAIResponse: null,
      awaitingUserInput: false,
      questionsPending: 0,
      activeUsers: new Set(),
      conversationPhase: 'opening'
    }
    
    this.states.set(groupId, { ...current, ...update })
  }
  
  static getState(groupId: string): ConversationStateTracker | null {
    return this.states.get(groupId) || null
  }
  
  static onUserMessage(groupId: string, userId: string, hasQuestion: boolean): void {
    const state = this.getState(groupId)
    const activeUsers = new Set(state?.activeUsers || [])
    activeUsers.add(userId)
    
    this.updateState(groupId, {
      lastActivity: new Date(),
      activeUsers,
      questionsPending: hasQuestion ? (state?.questionsPending || 0) + 1 : (state?.questionsPending || 0),
      conversationPhase: 'active'
    })
    
    // Cancel any pending AI response since user is active
    ResponseTimingManager.onNewUserMessage(groupId)
  }
  
  static onAIResponse(groupId: string): void {
    this.updateState(groupId, {
      lastAIResponse: new Date(),
      awaitingUserInput: true,
      questionsPending: 0
    })
  }
  
  static shouldAIWait(groupId: string, messageContext?: any): ResponseTiming {
    const state = this.getState(groupId)
    if (!state) {
      return {
        shouldWait: false,
        waitingFor: 'none',
        reason: 'No conversation state',
        responseType: 'immediate'
      }
    }

    // If message context indicates waiting is needed
    if (messageContext?.needsSpecificAnswer) {
      return {
        shouldWait: true,
        waitingFor: 'any_user',
        reason: 'Question requires personal/specific answer from group members',
        responseType: 'wait_for_answer',
        maxWaitTime: 5 * 60 * 1000 // 5 minutes max
      }
    }

    if (messageContext?.isDirectedQuestion && !messageContext?.mentionedUsers?.includes('ai')) {
      return {
        shouldWait: true,
        waitingFor: 'any_user',
        reason: 'Question directed at group members, not AI',
        responseType: 'wait_for_answer',
        maxWaitTime: 3 * 60 * 1000 // 3 minutes max
      }
    }

    // Questions pending for AI - respond immediately
    if (state.questionsPending > 0 || messageContext?.mentionedUsers?.includes('ai')) {
      return {
        shouldWait: false,
        waitingFor: 'none',
        reason: 'AI mentioned or question for AI',
        responseType: 'immediate'
      }
    }

    // Conversation stalled - gentle nudge
    const timeSinceLastActivity = Date.now() - state.lastActivity.getTime()
    if (timeSinceLastActivity > 5 * 60 * 1000) { // 5 minutes
      return {
        shouldWait: false,
        waitingFor: 'none',
        reason: 'Conversation stalled, providing gentle nudge',
        responseType: 'gentle_nudge'
      }
    }

    // Normal flow - respond immediately unless context suggests otherwise
    return {
      shouldWait: false,
      waitingFor: 'none',
      reason: 'Normal conversation flow',
      responseType: 'immediate'
    }
  }
  
  static cleanup(): void {
    // Clean up old conversation states (older than 24 hours)
    const cutoff = Date.now() - (24 * 60 * 60 * 1000)
    
    for (const [groupId, state] of this.states.entries()) {
      if (state.lastActivity.getTime() < cutoff) {
        this.states.delete(groupId)
        ResponseTimingManager.clearPendingResponse(groupId)
      }
    }
  }
}

// Auto-cleanup every hour
setInterval(() => {
  ConversationStateManager.cleanup()
}, 60 * 60 * 1000)

// Utility functions for message analysis
export function detectQuestionInMessage(content: string): boolean {
  const questionMarkers = [
    '?',
    'what do you think',
    'how do you',
    'why do you',
    'what would you',
    'do you think',
    'what about',
    'how about'
  ]
  
  const lowerContent = content.toLowerCase()
  return questionMarkers.some(marker => lowerContent.includes(marker))
}

export function detectConversationEnders(content: string): boolean {
  const enderMarkers = [
    'thanks',
    'thank you',
    'that helps',
    'got it',
    'makes sense',
    'perfect',
    'great',
    'awesome',
    'sounds good'
  ]
  
  const lowerContent = content.toLowerCase()
  return enderMarkers.some(marker => lowerContent.includes(marker))
}

export function calculateResponseUrgency(
  hasQuestion: boolean,
  timeSinceLastMessage: number,
  activeUserCount: number,
  timeSinceLastAI: number
): 'high' | 'medium' | 'low' {
  if (hasQuestion && timeSinceLastMessage < 30000) return 'high'
  if (activeUserCount > 1 && timeSinceLastMessage < 60000) return 'low'
  if (timeSinceLastAI < 30000) return 'low'
  if (timeSinceLastMessage > 300000) return 'high'
  
  return 'medium'
}
