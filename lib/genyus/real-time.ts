// Real-time messaging utilities for OnlyGenyus group chats
// Uses Supabase real-time subscriptions for instant messaging

import { createSupabaseClient } from '@/lib/supabase/client'
import type { RealtimeChannel } from '@supabase/supabase-js'

export class GroupChatRealTime {
  private groupId: string
  private userId: string
  private onNewMessage: (message: any) => void
  private onError: (error: string) => void
  private supabase = createSupabaseClient()
  private channel: RealtimeChannel | null = null

  constructor(
    groupId: string,
    userId: string,
    onNewMessage: (message: any) => void,
    onError: (error: string) => void = () => {}
  ) {
    this.groupId = groupId
    this.userId = userId
    this.onNewMessage = onNewMessage
    this.onError = onError
  }

  start() {
    console.log('Starting real-time subscription for group:', this.groupId)
    this.stop() // Clean up any existing subscription

    // Create real-time subscription for new messages in this group
    this.channel = this.supabase
      .channel(`group-chat-${this.groupId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'genyus_group_messages',
          filter: `group_id=eq.${this.groupId}`
        },
        async (payload) => {
          console.log('Real-time message received:', payload)

          // Always notify about new messages (including AI responses to our messages)
          // The frontend will handle deduplication if needed

          try {
            // Get user details for the message
            const { data: user } = await this.supabase
              .from('users')
              .select('id, name, email, profile_picture_url')
              .eq('id', payload.new.user_id)
              .single()

            // Format message for the UI
            const message = {
              id: payload.new.id,
              type: payload.new.message_type,
              content: payload.new.content,
              timestamp: new Date(payload.new.created_at),
              user: {
                id: payload.new.user_id,
                name: user?.name || user?.email || 'Unknown',
                profilePictureUrl: user?.profile_picture_url
              }
            }

            this.onNewMessage(message)
          } catch (error) {
            console.error('Error processing real-time message:', error)
            this.onError('Failed to process new message')
          }
        }
      )
      .subscribe((status) => {
        console.log('Real-time subscription status:', status)
        if (status === 'SUBSCRIPTION_ERROR') {
          this.onError('Real-time connection failed')
        }
      })
  }

  // Legacy method for compatibility - now just triggers a manual refresh
  checkForNewMessages() {
    console.log('Manual message check requested (real-time should handle this automatically)')
  }

  stop() {
    console.log('Stopping real-time subscription for group:', this.groupId)
    if (this.channel) {
      this.supabase.removeChannel(this.channel)
      this.channel = null
    }
  }

  // Legacy method for compatibility - no longer needed with real-time
  updateLastMessageTimestamp(timestamp: string) {
    console.log('updateLastMessageTimestamp called (no longer needed with real-time)')
  }
}

// Hook for using real-time group chat
export function useGroupChatRealTime(
  groupId: string | null,
  userId: string,
  onNewMessage: (message: any) => void,
  onError?: (error: string) => void
) {
  let realTimeInstance: GroupChatRealTime | null = null

  const start = () => {
    if (!groupId || !userId) return

    stop() // Stop any existing instance
    
    realTimeInstance = new GroupChatRealTime(
      groupId,
      userId,
      onNewMessage,
      onError
    )
    realTimeInstance.start()
  }

  const stop = () => {
    if (realTimeInstance) {
      realTimeInstance.stop()
      realTimeInstance = null
    }
  }

  const updateLastMessage = (timestamp: string) => {
    if (realTimeInstance) {
      realTimeInstance.updateLastMessageTimestamp(timestamp)
    }
  }

  const checkForNewMessages = () => {
    if (realTimeInstance) {
      realTimeInstance.checkForNewMessages()
    }
  }

  return { start, stop, updateLastMessage, checkForNewMessages }
}
