import { createClient } from "@supabase/supabase-js";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface ConversationSession {
  id: string;
  title: string;
  topic_category: string;
  session_summary: string;
  started_at: string;
  ended_at: string | null;
  message_count: number;
}

// Generate AI title for conversation
async function generateConversationTitle(messages: Array<{role: string, content: string}>): Promise<{title: string, category: string, summary: string}> {
  try {
    // Use the first few messages to generate a title
    const conversationPreview = messages.slice(0, 6).map(m => 
      `${m.role}: ${m.content.substring(0, 100)}...`
    ).join('\n');

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `Generate a concise, descriptive title for this conversation. Also categorize it and provide a brief summary.

Return JSON format:
{
  "title": "Brief descriptive title (max 50 chars)",
  "category": "work|personal|learning|creative|planning|other",
  "summary": "One sentence summary of the conversation"
}

Make titles specific and helpful for finding conversations later.`
          },
          {
            role: 'user',
            content: `Conversation preview:\n${conversationPreview}`
          }
        ],
        temperature: 0.3,
        max_tokens: 200
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const result = JSON.parse(data.choices[0].message.content);
    
    return {
      title: result.title || 'Untitled Conversation',
      category: result.category || 'other',
      summary: result.summary || 'No summary available'
    };
  } catch (error) {
    console.error('Error generating conversation title:', error);
    // Fallback to simple title generation
    const firstUserMessage = messages.find(m => m.role === 'user')?.content || '';
    const title = firstUserMessage.length > 50 
      ? firstUserMessage.substring(0, 47) + '...'
      : firstUserMessage || 'Untitled Conversation';
    
    return {
      title,
      category: 'other',
      summary: 'Conversation summary unavailable'
    };
  }
}

// Save conversation session with AI-generated title
export async function saveConversationSession(
  userId: string,
  messages: Array<{role: string, content: string}>,
  requestIds: string[]
): Promise<string | null> {
  if (messages.length < 2) return null; // Need at least one exchange
  
  const supabase = createSupabaseServiceClient();
  
  try {
    // Generate AI title and metadata
    const { title, category, summary } = await generateConversationTitle(messages);
    
    // Create conversation session
    const { data: session, error: sessionError } = await supabase
      .from('genyus_conversation_sessions')
      .insert({
        user_id: userId,
        title,
        topic_category: category,
        session_summary: summary,
        started_at: new Date().toISOString(),
        ended_at: new Date().toISOString(),
        message_count: messages.length
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Error creating conversation session:', sessionError);
      return null;
    }

    // Link requests to this session
    if (requestIds.length > 0) {
      const { error: contextError } = await supabase
        .from('genyus_conversation_context')
        .insert(
          requestIds.map(requestId => ({
            user_id: userId,
            session_id: session.id,
            request_id: requestId,
            message_type: 'question'
          }))
        );

      if (contextError) {
        console.error('Error linking requests to session:', contextError);
      }
    }

    console.log(`💾 Saved conversation session: "${title}" (${messages.length} messages)`);
    return session.id;
    
  } catch (error) {
    console.error('Error saving conversation session:', error);
    return null;
  }
}

// Get conversation sessions for history
export async function getConversationSessions(
  userId: string,
  limit: number = 20,
  offset: number = 0
): Promise<{sessions: ConversationSession[], total: number}> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get sessions with pagination
    const { data: sessions, error: sessionsError } = await supabase
      .from('genyus_conversation_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (sessionsError) {
      console.error('Error fetching conversation sessions:', sessionsError);
      return { sessions: [], total: 0 };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('genyus_conversation_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      console.error('Error counting conversation sessions:', countError);
    }

    return {
      sessions: sessions || [],
      total: count || 0
    };
    
  } catch (error) {
    console.error('Error in getConversationSessions:', error);
    return { sessions: [], total: 0 };
  }
}

// Get messages for a specific conversation session
export async function getSessionMessages(
  userId: string,
  sessionId: string
): Promise<Array<{role: string, content: string, timestamp: string}>> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get request IDs for this session
    const { data: context, error: contextError } = await supabase
      .from('genyus_conversation_context')
      .select('request_id')
      .eq('user_id', userId)
      .eq('session_id', sessionId);

    if (contextError || !context) {
      console.error('Error fetching session context:', contextError);
      return [];
    }

    const requestIds = context.map(c => c.request_id);
    
    if (requestIds.length === 0) return [];

    // Get messages for these requests
    const { data: requests, error: requestsError } = await supabase
      .from('genyus_requests')
      .select(`
        question,
        created_at,
        genyus_answers!inner(final_text, created_at)
      `)
      .in('id', requestIds)
      .order('created_at', { ascending: true });

    if (requestsError) {
      console.error('Error fetching session messages:', requestsError);
      return [];
    }

    // Format as conversation messages
    const messages: Array<{role: string, content: string, timestamp: string}> = [];
    
    for (const request of requests || []) {
      const answer = request.genyus_answers[0];
      if (answer) {
        messages.push({
          role: 'user',
          content: request.question,
          timestamp: request.created_at
        });
        messages.push({
          role: 'assistant',
          content: answer.final_text,
          timestamp: answer.created_at
        });
      }
    }

    return messages;
    
  } catch (error) {
    console.error('Error in getSessionMessages:', error);
    return [];
  }
}

// Auto-save conversation when it reaches certain thresholds
export async function autoSaveConversationIfNeeded(
  userId: string,
  currentMessages: Array<{role: string, content: string}>,
  requestIds: string[]
): Promise<void> {
  // Auto-save after 6+ messages (3+ exchanges) or if conversation seems complete
  if (currentMessages.length >= 6) {
    const lastMessage = currentMessages[currentMessages.length - 1];
    
    // Save if it's been a substantial conversation
    if (lastMessage && (
      lastMessage.content.includes('thank') ||
      lastMessage.content.includes('great') ||
      lastMessage.content.includes('perfect') ||
      currentMessages.length >= 10
    )) {
      await saveConversationSession(userId, currentMessages, requestIds);
    }
  }
}
