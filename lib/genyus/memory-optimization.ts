import { createClient } from "@supabase/supabase-js";
import { memoryManager } from "./memory-management";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface MemoryOptimizationStrategy {
  name: string;
  description: string;
  triggerConditions: {
    conversationCount?: number;
    daysSinceLastOptimization?: number;
    memorySize?: number;
  };
  execute: (userId: string) => Promise<void>;
}

export class MemoryOptimizationEngine {
  private supabase = createSupabaseServiceClient();
  
  private strategies: MemoryOptimizationStrategy[] = [
    {
      name: 'monthly_compression',
      description: 'Compress conversations older than 30 days into summaries (cost-efficient)',
      triggerConditions: {
        daysSinceLastOptimization: 30  // Less frequent = lower processing costs
      },
      execute: this.executeMonthlyCompression.bind(this)
    },
    {
      name: 'insight_extraction',
      description: 'Extract long-term insights from conversation patterns',
      triggerConditions: {
        conversationCount: 10
      },
      execute: this.executeInsightExtraction.bind(this)
    },
    {
      name: 'memory_consolidation',
      description: 'Consolidate similar insights and remove duplicates',
      triggerConditions: {
        daysSinceLastOptimization: 14
      },
      execute: this.executeMemoryConsolidation.bind(this)
    },
    {
      name: 'relationship_milestone_detection',
      description: 'Detect and record relationship milestones',
      triggerConditions: {
        conversationCount: 5
      },
      execute: this.executeRelationshipMilestoneDetection.bind(this)
    },
    {
      name: 'adaptive_memory_sizing',
      description: 'Adjust memory allocation based on user engagement patterns',
      triggerConditions: {
        daysSinceLastOptimization: 30
      },
      execute: this.executeAdaptiveMemorySizing.bind(this)
    }
  ];

  // Main optimization runner - can be called by cron job
  async optimizeUserMemory(userId: string): Promise<{
    strategiesExecuted: string[];
    optimizationResults: Record<string, any>;
  }> {
    const results: Record<string, any> = {};
    const executedStrategies: string[] = [];

    // Get user's current memory state
    const memoryState = await this.getUserMemoryState(userId);
    
    // Check which strategies should be executed
    for (const strategy of this.strategies) {
      if (await this.shouldExecuteStrategy(strategy, memoryState)) {
        try {
          console.log(`Executing strategy: ${strategy.name} for user: ${userId}`);
          await strategy.execute(userId);
          executedStrategies.push(strategy.name);
          results[strategy.name] = { status: 'success', executedAt: new Date() };
        } catch (error) {
          console.error(`Error executing strategy ${strategy.name}:`, error);
          results[strategy.name] = { status: 'error', error: error.message };
        }
      }
    }

    // Update optimization timestamp
    await this.updateOptimizationTimestamp(userId);

    return {
      strategiesExecuted: executedStrategies,
      optimizationResults: results
    };
  }

  // Optimize all active users (for cron job)
  async optimizeAllUsers(): Promise<void> {
    // Get users who have had conversations in the last 30 days
    const { data: activeUsers } = await this.supabase
      .from('genyus_requests')
      .select('user_id')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      .group('user_id');

    if (!activeUsers) return;

    const uniqueUsers = [...new Set(activeUsers.map(u => u.user_id))];
    
    console.log(`Starting memory optimization for ${uniqueUsers.length} active users`);

    // Process users in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < uniqueUsers.length; i += batchSize) {
      const batch = uniqueUsers.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(userId => 
          this.optimizeUserMemory(userId).catch(error => 
            console.error(`Failed to optimize memory for user ${userId}:`, error)
          )
        )
      );
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('Memory optimization completed for all active users');
  }

  private async getUserMemoryState(userId: string): Promise<{
    conversationCount: number;
    lastOptimization: Date | null;
    memorySize: number;
    relationshipStage: string;
    trustLevel: number;
  }> {
    // Get conversation count
    const { count: conversationCount } = await this.supabase
      .from('genyus_requests')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Get user memory profile
    const { data: userMemory } = await this.supabase
      .from('genyus_user_memory')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Estimate memory size (rough calculation)
    const { data: recentConversations } = await this.supabase
      .from('genyus_requests')
      .select('question, genyus_answers(final_text)')
      .eq('user_id', userId)
      .limit(50);

    const memorySize = recentConversations?.reduce((size, conv) => {
      return size + conv.question.length + (conv.genyus_answers[0]?.final_text?.length || 0);
    }, 0) || 0;

    return {
      conversationCount: conversationCount || 0,
      lastOptimization: userMemory?.updated_at ? new Date(userMemory.updated_at) : null,
      memorySize,
      relationshipStage: userMemory?.relationship_stage || 'new',
      trustLevel: userMemory?.trust_level || 1
    };
  }

  private async shouldExecuteStrategy(
    strategy: MemoryOptimizationStrategy,
    memoryState: any
  ): Promise<boolean> {
    const conditions = strategy.triggerConditions;

    // Check conversation count condition
    if (conditions.conversationCount && memoryState.conversationCount < conditions.conversationCount) {
      return false;
    }

    // Check days since last optimization
    if (conditions.daysSinceLastOptimization && memoryState.lastOptimization) {
      const daysSince = (Date.now() - memoryState.lastOptimization.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSince < conditions.daysSinceLastOptimization) {
        return false;
      }
    }

    // Check memory size condition
    if (conditions.memorySize && memoryState.memorySize < conditions.memorySize) {
      return false;
    }

    return true;
  }

  private async executeMonthlyCompression(userId: string): Promise<void> {
    // Compress conversations older than 30 days (instead of 7) for cost efficiency
    await memoryManager.compressOldConversations(userId);
  }

  private async executeInsightExtraction(userId: string): Promise<void> {
    await memoryManager.extractLongTermInsights(userId);
  }

  private async executeMemoryConsolidation(userId: string): Promise<void> {
    // Get all insights for the user
    const { data: insights } = await this.supabase
      .from('genyus_memory_insights')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (!insights || insights.length === 0) return;

    // Group similar insights
    const groupedInsights = this.groupSimilarInsights(insights);
    
    // Consolidate each group
    for (const group of groupedInsights) {
      if (group.length > 1) {
        await this.consolidateInsightGroup(userId, group);
      }
    }
  }

  private async executeRelationshipMilestoneDetection(userId: string): Promise<void> {
    const { data: userMemory } = await this.supabase
      .from('genyus_user_memory')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!userMemory) return;

    const milestones = [];
    
    // Detect milestones based on interaction count and trust level
    if (userMemory.interaction_count >= 5 && userMemory.relationship_stage === 'new') {
      milestones.push({
        milestone: 'First meaningful conversations',
        significance: 'User began engaging regularly with OnlyGenyus'
      });
    }
    
    if (userMemory.interaction_count >= 20 && userMemory.relationship_stage === 'developing') {
      milestones.push({
        milestone: 'Established regular usage pattern',
        significance: 'User demonstrates consistent engagement and trust'
      });
    }
    
    if (userMemory.trust_level >= 7) {
      milestones.push({
        milestone: 'High trust relationship achieved',
        significance: 'User shows strong confidence in OnlyGenyus responses'
      });
    }

    // Store milestones (would need to add this to the user memory structure)
    if (milestones.length > 0) {
      const currentMilestones = userMemory.relationship_milestones || [];
      const updatedMilestones = [...currentMilestones, ...milestones.map(m => ({
        ...m,
        date: new Date()
      }))];
      
      await this.supabase
        .from('genyus_user_memory')
        .update({ relationship_milestones: updatedMilestones })
        .eq('user_id', userId);
    }
  }

  private async executeAdaptiveMemorySizing(userId: string): Promise<void> {
    const memoryState = await this.getUserMemoryState(userId);
    
    // Adjust memory allocation based on user engagement
    let memoryAllocation = 'standard'; // standard, expanded, premium
    
    if (memoryState.conversationCount > 100 && memoryState.trustLevel > 7) {
      memoryAllocation = 'premium';
    } else if (memoryState.conversationCount > 50 && memoryState.trustLevel > 5) {
      memoryAllocation = 'expanded';
    }
    
    // Store memory allocation preference
    await this.supabase
      .from('genyus_user_memory')
      .update({ 
        memory_allocation: memoryAllocation,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);
  }

  private groupSimilarInsights(insights: any[]): any[][] {
    const groups: any[][] = [];
    const processed = new Set<string>();
    
    for (const insight of insights) {
      if (processed.has(insight.id)) continue;
      
      const similarInsights = insights.filter(other => 
        !processed.has(other.id) && 
        this.areInsightsSimilar(insight, other)
      );
      
      if (similarInsights.length > 1) {
        groups.push(similarInsights);
        similarInsights.forEach(si => processed.add(si.id));
      }
    }
    
    return groups;
  }

  private areInsightsSimilar(insight1: any, insight2: any): boolean {
    // Simple similarity check - could be enhanced with NLP
    if (insight1.insight_type !== insight2.insight_type) return false;
    
    const content1 = insight1.insight_data?.content || '';
    const content2 = insight2.insight_data?.content || '';
    
    // Check for keyword overlap
    const words1 = content1.toLowerCase().split(/\s+/);
    const words2 = content2.toLowerCase().split(/\s+/);
    const overlap = words1.filter(word => words2.includes(word)).length;
    
    return overlap / Math.max(words1.length, words2.length) > 0.5;
  }

  private async consolidateInsightGroup(userId: string, insights: any[]): Promise<void> {
    // Create a consolidated insight
    const consolidatedContent = insights.map(i => i.insight_data?.content).join('; ');
    const avgConfidence = insights.reduce((sum, i) => sum + i.confidence_score, 0) / insights.length;
    const maxImportance = Math.max(...insights.map(i => i.insight_data?.importance || 5));
    
    // Create new consolidated insight
    await this.supabase
      .from('genyus_memory_insights')
      .insert({
        user_id: userId,
        insight_type: insights[0].insight_type,
        insight_data: {
          content: consolidatedContent,
          importance: maxImportance,
          consolidated_from: insights.length
        },
        confidence_score: avgConfidence,
        supporting_messages: insights.flatMap(i => i.supporting_messages || []),
        first_observed: Math.min(...insights.map(i => new Date(i.first_observed).getTime())),
        last_confirmed: new Date().toISOString(),
        is_active: true
      });
    
    // Deactivate original insights
    const insightIds = insights.map(i => i.id);
    await this.supabase
      .from('genyus_memory_insights')
      .update({ is_active: false })
      .in('id', insightIds);
  }

  private async updateOptimizationTimestamp(userId: string): Promise<void> {
    await this.supabase
      .from('genyus_user_memory')
      .update({ 
        updated_at: new Date().toISOString(),
        last_optimization: new Date().toISOString()
      })
      .eq('user_id', userId);
  }
}

export const memoryOptimizer = new MemoryOptimizationEngine();

// Cron job function for Vercel/Netlify
export async function optimizeAllUserMemories() {
  try {
    await memoryOptimizer.optimizeAllUsers();
    return { success: true, message: 'Memory optimization completed' };
  } catch (error) {
    console.error('Memory optimization failed:', error);
    return { success: false, error: error.message };
  }
}
