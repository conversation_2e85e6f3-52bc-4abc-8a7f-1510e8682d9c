import { createClient } from "@supabase/supabase-js"
import { countChargeableWords } from "./word-count"
import { moderateContent, shouldBlockContent, generateRefusalMessage } from "./moderation"
import { callDeepSeekDirect } from "./providers"
import { ConversationIntelligence, ConversationContext, ConversationState } from './conversation-intelligence'
import { NameMemorySystem } from './name-memory'

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  )
}

interface GroupMember {
  id: string
  name: string
}

interface GroupContext {
  name: string
  members: GroupMember[]
}

interface GroupRequestParams {
  userId: string
  groupId: string
  message: string
  groupContext: GroupContext
}

export async function processGenyusGroupRequest({
  userId,
  groupId,
  message,
  groupContext
}: GroupRequestParams) {
  try {
    const startTime = Date.now()
    console.log('Processing group request:', { userId, groupId, message: message.substring(0, 50) + '...' })

    const supabase = createSupabaseServiceClient()

    // Moderation check
    const moderationResult = await moderateContent(message)
    if (shouldBlockContent(moderationResult)) {
      const refusalMessage = generateRefusalMessage(moderationResult)
      return createRefusalStream(refusalMessage)
    }

  // Get group conversation history (last 20 messages) - simplified approach
  const { data: groupHistory } = await supabase
    .from('genyus_group_messages')
    .select(`
      message_type,
      content,
      created_at,
      user_id
    `)
    .eq('group_id', groupId)
    .order('created_at', { ascending: false })
    .limit(20)

  console.log('Group history loaded:', groupHistory?.length || 0, 'messages')

  // Get all group members for name resolution - using separate queries to avoid foreign key issues
  const { data: memberIds } = await supabase
    .from('genyus_group_members')
    .select('user_id')
    .eq('group_id', groupId)
    .eq('is_active', true)

  let messageUsers: any[] = []
  if (memberIds && memberIds.length > 0) {
    const userIds = memberIds.map(m => m.user_id)
    const { data: userDetails } = await supabase
      .from('users')
      .select('id, name, email')
      .in('id', userIds)

    messageUsers = userDetails || []
  }

  // Get detailed member information for name learning - using the same user data we already fetched
  const memberDetails = memberIds?.map(m => {
    const user = messageUsers.find(u => u.id === m.user_id)
    return {
      user_id: m.user_id,
      joined_at: new Date(), // We don't have this data easily available, but it's not critical
      users: user || { name: 'Unknown', email: '', bio: '', avatar: '', created_at: new Date() }
    }
  }) || []

  // Build conversation context for AI with enhanced group awareness
  const conversationHistory = (groupHistory || [])
    .reverse() // Oldest first
    .map(msg => {
      if (msg.message_type === 'user') {
        const user = messageUsers.find(u => u.id === msg.user_id)
        const userName = user?.name || user?.email || 'User'
        return {
          role: 'user' as const,
          content: `${userName}: ${msg.content}`
        }
      } else {
        return {
          role: 'assistant' as const,
          content: msg.content
        }
      }
    })

  // Add group context summary if this is the first message in a while
  const recentMessageCount = conversationHistory.length
  if (recentMessageCount === 0) {
    // First message in group - add welcoming context
    const activeMemberCount = groupContext.members.length
    const contextMessage = activeMemberCount === 1
      ? `This is the start of a new conversation in the "${groupContext.name}" group chat. The conversation starter is here, and others may join later. Feel free to begin the discussion and leave space for others to contribute when they arrive.`
      : `This is the start of a new conversation in the "${groupContext.name}" group chat. Welcome everyone and encourage participation.`

    conversationHistory.unshift({
      role: 'user' as const,
      content: `System: ${contextMessage}`
    })
  }

  // Build comprehensive name memory and group context
  const groupMemory = NameMemorySystem.buildGroupMemory(
    groupId,
    memberDetails || [],
    groupHistory || []
  )

  // Analyze conversation context for intelligent response timing
  const conversationContext: ConversationContext = {
    recentMessages: (groupHistory || []).map(msg => {
      const user = messageUsers.find(u => u.id === msg.user_id)
      return {
        userId: msg.user_id,
        userName: groupMemory.namePreferences[msg.user_id] || user?.name || user?.email || 'Unknown',
        content: msg.content,
        timestamp: new Date(msg.created_at),
        messageType: msg.message_type as 'user' | 'assistant'
      }
    }),
    groupMembers: groupMemory.members.map(member => ({
      id: member.id,
      name: groupMemory.namePreferences[member.id] || member.name,
      isActive: true
    })),
    conversationState: {
      phase: 'discussion',
      lastAIResponse: groupHistory?.find(msg => msg.message_type === 'assistant')
        ? new Date(groupHistory.find(msg => msg.message_type === 'assistant')!.created_at)
        : null,
      pendingQuestions: [],
      activeParticipants: [...new Set((groupHistory || []).slice(0, 5).map(msg => msg.user_id))],
      conversationTopic: null,
      energyLevel: 'medium',
      waitingFor: 'none'
    }
  }

  // Get AI response strategy
  console.log('⏱️ Starting conversation analysis at:', Date.now() - startTime, 'ms')
  const responseStrategy = ConversationIntelligence.analyzeConversationContext(conversationContext)
  console.log('⏱️ Conversation analysis done at:', Date.now() - startTime, 'ms')

  // If AI should wait, return a waiting response
  if (!responseStrategy.shouldRespond) {
    return createWaitingResponse(responseStrategy, groupContext)
  }

  // Create comprehensive system prompt with names, intelligence, and context
  const groupSystemPrompt = createGroupSystemPrompt(groupContext)
  const namePrompt = NameMemorySystem.generateNamePrompt(groupMemory)
  const contextPrompt = ConversationIntelligence.generateContextPrompt(responseStrategy, conversationContext)
  const enhancedSystemPrompt = groupSystemPrompt + namePrompt + contextPrompt

  // Store the request in genyus_requests table
  const { data: request, error: reqError } = await supabase
    .from('genyus_requests')
    .insert({
      user_id: userId,
      question: message
    })
    .select('id')
    .single()

  if (reqError || !request) {
    throw new Error('Failed to store request')
  }

  let finalText = ''
  let wordCount = 0

  // Call AI with enhanced group context and conversation intelligence
  console.log('⏱️ Starting AI call at:', Date.now() - startTime, 'ms')
  const aiResponse = await callDeepSeekDirect(
    message,
    [
      { role: 'system', content: enhancedSystemPrompt },
      ...conversationHistory
    ]
  )
  console.log('⏱️ AI call completed at:', Date.now() - startTime, 'ms')

  // Create streaming response that stores the final result
  console.log('⏱️ Starting stream processing at:', Date.now() - startTime, 'ms')
  const stream = new ReadableStream<string>({
    async start(controller) {
      const reader = aiResponse.stream.getReader()

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          finalText += value
          controller.enqueue(value)
        }

        // Store final AI response
        console.log('⏱️ Starting database operations at:', Date.now() - startTime, 'ms')
        wordCount = countChargeableWords(finalText)

        const { data: answer, error: answerError } = await supabase
          .from('genyus_answers')
          .insert({
            request_id: request.id,
            moderator_model: 'deepseek-chat',
            final_text: finalText,
            word_count: wordCount,
            cost_usd: 0.001 * wordCount // Approximate cost
          })
          .select('id')
          .single()

        if (!answerError && answer) {
          // Store group message for AI response
          await supabase
            .from('genyus_group_messages')
            .insert({
              group_id: groupId,
              user_id: userId, // The user who triggered the AI response
              message_type: 'assistant',
              content: finalText,
              genyus_request_id: request.id,
              genyus_answer_id: answer.id
            })
        }

        // Deduct tokens from user who sent the message
        await supabase.rpc('deduct_user_words', {
          p_user_id: userId,
          p_word_count: wordCount
        })

        console.log('⏱️ All operations completed at:', Date.now() - startTime, 'ms')

      } catch (error) {
        console.error('Group AI response error:', error)
        controller.error(error)
      } finally {
        controller.close()
      }
    }
  })

  return {
    stream,
    getFinalText: () => Promise.resolve(finalText),
    getWordCount: () => Promise.resolve(wordCount)
  }

  } catch (error) {
    console.error('Group orchestrator error:', error)
    throw error
  }
}

// Create a waiting response when AI should pause
function createWaitingResponse(strategy: any, groupContext: GroupContext) {
  const waitingMessages = [
    "I'm curious to hear what others think about this...",
    "Let me give everyone a moment to share their thoughts.",
    "I'd love to hear from the rest of the group on this.",
    "What does everyone else think? I'll wait for your input.",
    "I'm interested in hearing different perspectives on this."
  ]

  const randomMessage = waitingMessages[Math.floor(Math.random() * waitingMessages.length)]

  return {
    stream: new ReadableStream<string>({
      start(controller) {
        controller.enqueue(randomMessage)
        controller.close()
      }
    }),
    getFinalText: () => Promise.resolve(randomMessage),
    getWordCount: () => Promise.resolve(countChargeableWords(randomMessage))
  }
}

function createGroupSystemPrompt(groupContext: GroupContext): string {
  const memberNames = groupContext.members.map(m => m.name).join(', ')
  const memberCount = groupContext.members.length

  return `You are OnlyGenyus, an AI assistant participating in a group chat called "${groupContext.name}" with ${memberCount} members: ${memberNames}.

IMPORTANT GROUP CHAT GUIDELINES:
- Treat this like a real meeting or group conversation
- Address the group naturally, as if you're sitting around a table together
- Reference members by name when responding to their specific questions or comments
- Ask follow-up questions to encourage participation from different members
- Facilitate discussion by connecting ideas between members
- Be conversational, helpful, and engaging for the group dynamic

GROUP CONVERSATION TECHNIQUES:
- Use phrases like "What does everyone think about..." or "Sarah, you mentioned earlier..."
- Acknowledge when someone makes a good point: "Great point, Mike!"
- Ask directed questions: "Lisa, have you experienced something similar?"
- Summarize group discussions when helpful
- Encourage quieter members to participate

ASYNC CONVERSATION SUPPORT:
- If some members haven't joined yet, acknowledge this naturally: "When [Name] joins, they might have insights on this too"
- Leave conversation threads open for others to contribute later
- Provide helpful context for members who join mid-conversation
- Don't rush to conclusions - let conversations develop over time
- It's okay to pause and wait for more input from the group

PRIVACY & CONTEXT:
- You only have access to this group conversation - no individual chat histories
- Keep responses relevant to the group discussion
- Don't reference private information that wouldn't be known to the group

TONE & STYLE:
- Be friendly, professional, and genuinely helpful
- Match the energy and tone of the group
- Keep responses concise but thorough
- Use natural group conversation language

Remember: You're not just answering questions - you're participating in a group discussion as a helpful AI team member.`
}

function createRefusalStream(message: string) {
  return {
    stream: new ReadableStream<string>({
      start(controller) {
        controller.enqueue(message)
        controller.close()
      }
    }),
    getFinalText: () => Promise.resolve(message),
    getWordCount: () => Promise.resolve(0)
  }
}
