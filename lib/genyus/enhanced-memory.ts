import { createClient } from "@supabase/supabase-js";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface UserMemoryProfile {
  user_id: string;
  communication_style: {
    vocabulary_level?: number; // 1-10 scale
    formality_preference?: 'casual' | 'professional' | 'adaptive';
    humor_appreciation?: boolean;
    detail_preference?: 'concise' | 'detailed' | 'adaptive';
    learning_style?: 'visual' | 'analytical' | 'practical' | 'creative';
  };
  preferred_response_length: 'short' | 'medium' | 'long' | 'adaptive';
  preferred_tone: 'encouraging' | 'professional' | 'casual' | 'academic' | 'friendly';
  relationship_stage: 'new' | 'developing' | 'established' | 'trusted';
  trust_level: number; // 1-10
  interaction_count: number;
  positive_feedback_count: number;
  interests: string[];
  expertise_areas: string[];
  goals: string[];
  challenges: string[];
  successful_response_patterns: Record<string, any>;
  communication_adaptations: Record<string, any>;
  recent_topics: string[];
  ongoing_projects: string[];
  follow_up_items: string[];
  last_interaction?: Date;
}

export interface ConversationSession {
  id: string;
  user_id: string;
  title?: string;
  topic_category?: string;
  session_summary?: string;
  key_insights: string[];
  action_items: string[];
  started_at: Date;
  ended_at?: Date;
  message_count: number;
}

export interface ConversationContext {
  user_id: string;
  session_id?: string;
  request_id: string;
  message_type: 'question' | 'follow_up' | 'clarification' | 'feedback';
  emotional_tone?: string;
  complexity_level?: number;
  mentioned_topics: string[];
  mentioned_people: string[];
  mentioned_projects: string[];
  temporal_references: string[];
  satisfaction_indicators: Record<string, any>;
  follow_up_requested: boolean;
}

export async function getUserMemoryProfile(userId: string): Promise<UserMemoryProfile | null> {
  const supabase = createSupabaseServiceClient();
  
  try {
    const { data, error } = await supabase
      .from('genyus_user_memory')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // Not found is OK
      console.error('Error fetching user memory profile:', error);
      return null;
    }

    return data || null;
  } catch (error) {
    console.error('Error in getUserMemoryProfile:', error);
    return null;
  }
}

export async function initializeUserMemory(userId: string): Promise<void> {
  const supabase = createSupabaseServiceClient();
  
  try {
    await supabase.rpc('initialize_user_memory', { user_uuid: userId });
  } catch (error) {
    console.error('Error initializing user memory:', error);
  }
}

export async function updateUserMemoryProfile(
  userId: string, 
  updates: Partial<UserMemoryProfile>
): Promise<void> {
  const supabase = createSupabaseServiceClient();
  
  try {
    const { error } = await supabase
      .from('genyus_user_memory')
      .upsert({
        user_id: userId,
        ...updates,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error updating user memory profile:', error);
    }
  } catch (error) {
    console.error('Error in updateUserMemoryProfile:', error);
  }
}

export async function getEnhancedConversationHistory(
  userId: string, 
  limit: number = 10
): Promise<{
  messages: Array<{role: string, content: string, timestamp: Date}>;
  context: ConversationContext[];
  recentSessions: ConversationSession[];
}> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get recent conversation history with context
    const { data: requests, error: reqError } = await supabase
      .from('genyus_requests')
      .select(`
        id,
        question,
        created_at,
        genyus_answers!inner(final_text, created_at),
        genyus_conversation_context(
          message_type,
          emotional_tone,
          complexity_level,
          mentioned_topics,
          mentioned_people,
          mentioned_projects,
          satisfaction_indicators,
          follow_up_requested
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (reqError) {
      console.error('Error fetching enhanced conversation history:', reqError);
      return { messages: [], context: [], recentSessions: [] };
    }

    // Get recent conversation sessions
    const { data: sessions, error: sessError } = await supabase
      .from('genyus_conversation_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .limit(5);

    if (sessError) {
      console.error('Error fetching conversation sessions:', sessError);
    }

    // Convert to conversation format
    const messages: Array<{role: string, content: string, timestamp: Date}> = [];
    const context: ConversationContext[] = [];
    
    for (const request of requests || []) {
      const answer = request.genyus_answers[0];
      const contextData = request.genyus_conversation_context[0];
      
      if (answer) {
        // Add user message
        messages.push({
          role: 'user',
          content: request.question,
          timestamp: new Date(request.created_at)
        });
        
        // Add assistant message
        messages.push({
          role: 'assistant',
          content: answer.final_text,
          timestamp: new Date(answer.created_at)
        });

        // Add context if available
        if (contextData) {
          context.push({
            user_id: userId,
            request_id: request.id,
            ...contextData
          });
        }
      }
    }

    // Sort by timestamp (oldest first for context)
    messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    return {
      messages,
      context,
      recentSessions: sessions || []
    };
    
  } catch (error) {
    console.error('Error in getEnhancedConversationHistory:', error);
    return { messages: [], context: [], recentSessions: [] };
  }
}

export async function analyzeAndStoreConversationContext(
  userId: string,
  requestId: string,
  question: string,
  sessionId?: string
): Promise<void> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Basic analysis of the question
    const analysis = analyzeMessage(question);
    
    const contextData: Partial<ConversationContext> = {
      user_id: userId,
      session_id: sessionId,
      request_id: requestId,
      message_type: analysis.messageType,
      emotional_tone: analysis.emotionalTone,
      complexity_level: analysis.complexityLevel,
      mentioned_topics: analysis.topics,
      mentioned_people: analysis.people,
      mentioned_projects: analysis.projects,
      temporal_references: analysis.timeReferences,
      satisfaction_indicators: {},
      follow_up_requested: analysis.followUpRequested
    };

    const { error } = await supabase
      .from('genyus_conversation_context')
      .insert(contextData);

    if (error) {
      console.error('Error storing conversation context:', error);
    }
  } catch (error) {
    console.error('Error in analyzeAndStoreConversationContext:', error);
  }
}

function analyzeMessage(message: string): {
  messageType: 'question' | 'follow_up' | 'clarification' | 'feedback';
  emotionalTone?: string;
  complexityLevel: number;
  topics: string[];
  people: string[];
  projects: string[];
  timeReferences: string[];
  followUpRequested: boolean;
} {
  const lowerMessage = message.toLowerCase();
  
  // Determine message type
  let messageType: 'question' | 'follow_up' | 'clarification' | 'feedback' = 'question';
  if (lowerMessage.includes('thanks') || lowerMessage.includes('great') || lowerMessage.includes('helpful')) {
    messageType = 'feedback';
  } else if (lowerMessage.includes('what do you mean') || lowerMessage.includes('can you explain')) {
    messageType = 'clarification';
  } else if (lowerMessage.includes('also') || lowerMessage.includes('additionally') || lowerMessage.includes('furthermore')) {
    messageType = 'follow_up';
  }

  // Detect emotional tone
  let emotionalTone: string | undefined;
  if (lowerMessage.includes('excited') || lowerMessage.includes('amazing') || lowerMessage.includes('love')) {
    emotionalTone = 'excited';
  } else if (lowerMessage.includes('frustrated') || lowerMessage.includes('difficult') || lowerMessage.includes('problem')) {
    emotionalTone = 'frustrated';
  } else if (lowerMessage.includes('curious') || lowerMessage.includes('wondering') || lowerMessage.includes('interested')) {
    emotionalTone = 'curious';
  }

  // Estimate complexity (1-10 based on length, vocabulary, concepts)
  const complexityLevel = Math.min(10, Math.max(1, Math.floor(message.length / 50) + 1));

  // Extract topics (simplified - could be enhanced with NLP)
  const topics: string[] = [];
  const topicKeywords = ['business', 'technology', 'health', 'education', 'finance', 'marketing', 'design', 'programming', 'writing', 'art'];
  topicKeywords.forEach(topic => {
    if (lowerMessage.includes(topic)) {
      topics.push(topic);
    }
  });

  // Extract people mentions (simplified)
  const people: string[] = [];
  const peopleMatches = message.match(/\b[A-Z][a-z]+ [A-Z][a-z]+\b/g);
  if (peopleMatches) {
    people.push(...peopleMatches);
  }

  // Extract project mentions (simplified)
  const projects: string[] = [];
  if (lowerMessage.includes('project') || lowerMessage.includes('working on')) {
    const projectMatches = message.match(/(?:project|working on) ([^.!?]+)/gi);
    if (projectMatches) {
      projects.push(...projectMatches.map(m => m.replace(/^(project|working on)\s+/i, '')));
    }
  }

  // Extract time references
  const timeReferences: string[] = [];
  const timeKeywords = ['yesterday', 'today', 'tomorrow', 'next week', 'last month', 'soon', 'later', 'now'];
  timeKeywords.forEach(time => {
    if (lowerMessage.includes(time)) {
      timeReferences.push(time);
    }
  });

  // Check if follow-up is requested
  const followUpRequested = lowerMessage.includes('follow up') || 
                           lowerMessage.includes('remind me') || 
                           lowerMessage.includes('check back') ||
                           lowerMessage.includes('later');

  return {
    messageType,
    emotionalTone,
    complexityLevel,
    topics,
    people,
    projects,
    timeReferences,
    followUpRequested
  };
}
