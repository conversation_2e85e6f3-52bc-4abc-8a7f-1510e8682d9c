import { createClient } from "@supabase/supabase-js";
import { updateUserMemoryProfile, getUserMemoryProfile } from "./enhanced-memory";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface CommunicationAnalysis {
  vocabularyLevel: number; // 1-10 scale
  formalityLevel: number; // 1-10 scale (1=very casual, 10=very formal)
  complexityPreference: number; // 1-10 scale
  emotionalExpressiveness: number; // 1-10 scale
  questioningStyle: 'direct' | 'exploratory' | 'analytical' | 'creative';
  responsePreference: 'concise' | 'detailed' | 'structured' | 'conversational';
  learningStyle: 'visual' | 'analytical' | 'practical' | 'creative' | 'mixed';
  communicationPatterns: string[];
}

export async function analyzeCommunicationStyle(
  userId: string,
  message: string,
  isFirstMessage: boolean = false
): Promise<CommunicationAnalysis> {
  
  const analysis: CommunicationAnalysis = {
    vocabularyLevel: analyzeVocabularyLevel(message),
    formalityLevel: analyzeFormalityLevel(message),
    complexityPreference: analyzeComplexityPreference(message),
    emotionalExpressiveness: analyzeEmotionalExpressiveness(message),
    questioningStyle: analyzeQuestioningStyle(message),
    responsePreference: analyzeResponsePreference(message),
    learningStyle: analyzeLearningStyle(message),
    communicationPatterns: identifyCommunicationPatterns(message)
  };

  // Store analysis and update user memory if this reveals new insights
  if (!isFirstMessage) {
    await updateCommunicationProfile(userId, analysis);
  }

  return analysis;
}

function analyzeVocabularyLevel(message: string): number {
  const words = message.toLowerCase().split(/\s+/);
  const totalWords = words.length;
  
  // Count sophisticated vocabulary
  const sophisticatedWords = words.filter(word => 
    word.length > 8 || // Long words
    /^(consequently|furthermore|nevertheless|specifically|particularly|significantly|comprehensive|sophisticated|implementation|optimization|methodology|paradigm|infrastructure|architecture|fundamental|substantial|considerable|exceptional|extraordinary|remarkable|phenomenal|tremendous|magnificent|brilliant|outstanding|excellent|superior|advanced|complex|intricate|elaborate|sophisticated|comprehensive|extensive|thorough|detailed|precise|accurate|meticulous|systematic|strategic|analytical|theoretical|practical|innovative|creative|original|unique|distinctive|exceptional|remarkable|notable|significant|important|crucial|essential|vital|critical|fundamental|primary|principal|major|substantial|considerable|extensive|comprehensive|thorough|complete|total|entire|whole|full|maximum|optimal|ideal|perfect|excellent|superior|outstanding|exceptional|extraordinary|remarkable|phenomenal|tremendous|magnificent|brilliant|amazing|incredible|fantastic|wonderful|marvelous|splendid|superb|terrific|fabulous|awesome|impressive|striking|stunning|breathtaking|spectacular|dramatic|powerful|strong|intense|profound|deep|meaningful|significant|important|valuable|useful|beneficial|advantageous|favorable|positive|constructive|productive|effective|efficient|successful|accomplished|achieved|attained|realized|fulfilled|completed|finished|concluded|resolved|solved|addressed|handled|managed|dealt|treated|processed|analyzed|examined|investigated|explored|studied|researched|discovered|found|identified|recognized|acknowledged|understood|comprehended|grasped|perceived|observed|noticed|detected|spotted|located|situated|positioned|placed|arranged|organized|structured|designed|created|developed|built|constructed|established|founded|formed|shaped|molded|crafted|produced|generated|manufactured|made|fabricated|assembled|compiled|composed|written|authored|penned|drafted|formulated|devised|conceived|invented|innovated|pioneered|introduced|launched|initiated|started|began|commenced|undertaken|embarked|engaged|involved|participated|contributed|collaborated|cooperated|worked|operated|functioned|performed|executed|implemented|applied|utilized|employed|used|adopted|embraced|accepted|approved|endorsed|supported|backed|promoted|advocated|recommended|suggested|proposed|offered|presented|submitted|provided|supplied|delivered|furnished|equipped|prepared|arranged|organized|planned|scheduled|coordinated|managed|supervised|directed|led|guided|instructed|taught|educated|trained|coached|mentored|advised|counseled|consulted|assisted|helped|aided|supported|encouraged|motivated|inspired|influenced|persuaded|convinced|assured|guaranteed|promised|committed|dedicated|devoted|focused|concentrated|emphasized|highlighted|stressed|underlined|underscored|accentuated|amplified|enhanced|improved|upgraded|advanced|progressed|developed|evolved|transformed|changed|modified|altered|adjusted|adapted|customized|tailored|personalized|individualized|specialized|focused|targeted|directed|aimed|intended|designed|planned|prepared|arranged|organized|structured|formatted|styled|presented|displayed|shown|exhibited|demonstrated|illustrated|depicted|portrayed|represented|symbolized|signified|indicated|suggested|implied|hinted|alluded|referred|mentioned|noted|observed|remarked|commented|stated|declared|announced|proclaimed|revealed|disclosed|exposed|uncovered|discovered|found|identified|recognized|acknowledged|admitted|confessed|conceded|agreed|accepted|approved|endorsed|supported|backed|promoted|advocated|recommended|suggested|proposed|offered|presented|submitted|provided|supplied|delivered|furnished|equipped|prepared|arranged|organized|planned|scheduled|coordinated|managed|supervised|directed|led|guided|instructed|taught|educated|trained|coached|mentored|advised|counseled|consulted|assisted|helped|aided|supported|encouraged|motivated|inspired|influenced|persuaded|convinced|assured|guaranteed|promised|committed|dedicated|devoted|focused|concentrated|emphasized|highlighted|stressed|underlined|underscored|accentuated|amplified|enhanced|improved|upgraded|advanced|progressed|developed|evolved|transformed|changed|modified|altered|adjusted|adapted|customized|tailored|personalized|individualized|specialized)/.test(word)
  ).length;
  
  // Calculate vocabulary sophistication ratio
  const sophisticationRatio = totalWords > 0 ? sophisticatedWords / totalWords : 0;
  
  // Convert to 1-10 scale
  return Math.min(10, Math.max(1, Math.round(1 + (sophisticationRatio * 9) + (totalWords > 50 ? 1 : 0))));
}

function analyzeFormalityLevel(message: string): number {
  const lowerMessage = message.toLowerCase();
  
  // Formal indicators
  const formalIndicators = [
    'please', 'thank you', 'would you', 'could you', 'i would like',
    'i am writing to', 'furthermore', 'moreover', 'consequently',
    'therefore', 'however', 'nevertheless', 'in addition', 'specifically'
  ];
  
  // Casual indicators
  const casualIndicators = [
    'hey', 'hi', 'yeah', 'yep', 'nope', 'gonna', 'wanna', 'kinda',
    'sorta', 'lol', 'haha', 'omg', 'btw', 'fyi', 'tbh', 'imo'
  ];
  
  const formalCount = formalIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
  const casualCount = casualIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
  
  // Check for contractions (casual) vs full forms (formal)
  const contractions = (lowerMessage.match(/\b\w+'\w+\b/g) || []).length;
  const hasProperPunctuation = /[.!?]$/.test(message.trim());
  
  // Calculate formality score
  let formalityScore = 5; // Start neutral
  formalityScore += formalCount * 0.5;
  formalityScore -= casualCount * 0.5;
  formalityScore -= contractions * 0.2;
  formalityScore += hasProperPunctuation ? 0.5 : -0.5;
  
  return Math.min(10, Math.max(1, Math.round(formalityScore)));
}

function analyzeComplexityPreference(message: string): number {
  const sentences = message.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length;
  
  // Count complex concepts
  const complexConcepts = [
    'strategy', 'methodology', 'framework', 'paradigm', 'algorithm',
    'optimization', 'implementation', 'architecture', 'infrastructure',
    'analysis', 'synthesis', 'evaluation', 'assessment', 'comparison'
  ];
  
  const complexConceptCount = complexConcepts.filter(concept => 
    message.toLowerCase().includes(concept)
  ).length;
  
  // Calculate complexity preference
  let complexityScore = 1;
  complexityScore += Math.min(4, avgSentenceLength / 5); // Longer sentences = higher complexity
  complexityScore += Math.min(3, complexConceptCount); // Complex concepts
  complexityScore += message.length > 200 ? 2 : 0; // Longer messages
  
  return Math.min(10, Math.max(1, Math.round(complexityScore)));
}

function analyzeEmotionalExpressiveness(message: string): number {
  const lowerMessage = message.toLowerCase();
  
  // Emotional indicators
  const emotionalWords = [
    'love', 'hate', 'excited', 'frustrated', 'amazing', 'terrible',
    'wonderful', 'awful', 'fantastic', 'horrible', 'brilliant', 'stupid',
    'incredible', 'disappointing', 'thrilled', 'devastated', 'ecstatic',
    'furious', 'delighted', 'annoyed', 'passionate', 'enthusiastic'
  ];
  
  const exclamationCount = (message.match(/!/g) || []).length;
  const capsCount = (message.match(/[A-Z]{2,}/g) || []).length;
  const emotionalWordCount = emotionalWords.filter(word => lowerMessage.includes(word)).length;
  const emojis = (message.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu) || []).length;
  
  let expressiveness = 1;
  expressiveness += Math.min(3, exclamationCount);
  expressiveness += Math.min(2, capsCount);
  expressiveness += Math.min(3, emotionalWordCount);
  expressiveness += Math.min(2, emojis);
  
  return Math.min(10, Math.max(1, Math.round(expressiveness)));
}

function analyzeQuestioningStyle(message: string): 'direct' | 'exploratory' | 'analytical' | 'creative' {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('what if') || lowerMessage.includes('imagine') || lowerMessage.includes('creative')) {
    return 'creative';
  } else if (lowerMessage.includes('analyze') || lowerMessage.includes('compare') || lowerMessage.includes('evaluate')) {
    return 'analytical';
  } else if (lowerMessage.includes('explore') || lowerMessage.includes('understand') || lowerMessage.includes('learn about')) {
    return 'exploratory';
  } else {
    return 'direct';
  }
}

function analyzeResponsePreference(message: string): 'concise' | 'detailed' | 'structured' | 'conversational' {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('briefly') || lowerMessage.includes('quick') || lowerMessage.includes('summary')) {
    return 'concise';
  } else if (lowerMessage.includes('detailed') || lowerMessage.includes('comprehensive') || lowerMessage.includes('thorough')) {
    return 'detailed';
  } else if (lowerMessage.includes('step by step') || lowerMessage.includes('organize') || lowerMessage.includes('structure')) {
    return 'structured';
  } else {
    return 'conversational';
  }
}

function analyzeLearningStyle(message: string): 'visual' | 'analytical' | 'practical' | 'creative' | 'mixed' {
  const lowerMessage = message.toLowerCase();
  
  const visualKeywords = ['show', 'see', 'visual', 'diagram', 'chart', 'image', 'picture'];
  const analyticalKeywords = ['analyze', 'data', 'statistics', 'research', 'study', 'evidence'];
  const practicalKeywords = ['how to', 'practical', 'apply', 'implement', 'use', 'do'];
  const creativeKeywords = ['creative', 'innovative', 'brainstorm', 'imagine', 'design'];
  
  const visualCount = visualKeywords.filter(word => lowerMessage.includes(word)).length;
  const analyticalCount = analyticalKeywords.filter(word => lowerMessage.includes(word)).length;
  const practicalCount = practicalKeywords.filter(word => lowerMessage.includes(word)).length;
  const creativeCount = creativeKeywords.filter(word => lowerMessage.includes(word)).length;
  
  const max = Math.max(visualCount, analyticalCount, practicalCount, creativeCount);
  
  if (max === 0) return 'mixed';
  if (visualCount === max) return 'visual';
  if (analyticalCount === max) return 'analytical';
  if (practicalCount === max) return 'practical';
  if (creativeCount === max) return 'creative';
  
  return 'mixed';
}

function identifyCommunicationPatterns(message: string): string[] {
  const patterns: string[] = [];
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('please') || lowerMessage.includes('thank you')) {
    patterns.push('polite');
  }
  
  if (lowerMessage.includes('i think') || lowerMessage.includes('in my opinion')) {
    patterns.push('opinion-sharing');
  }
  
  if (lowerMessage.includes('help me') || lowerMessage.includes('can you')) {
    patterns.push('help-seeking');
  }
  
  if (message.includes('?')) {
    patterns.push('questioning');
  }
  
  if (lowerMessage.includes('because') || lowerMessage.includes('since') || lowerMessage.includes('due to')) {
    patterns.push('reasoning');
  }
  
  return patterns;
}

async function updateCommunicationProfile(userId: string, analysis: CommunicationAnalysis): Promise<void> {
  try {
    const currentMemory = await getUserMemoryProfile(userId);
    
    if (currentMemory) {
      // Update communication style with weighted average (new analysis gets 30% weight)
      const currentStyle = currentMemory.communication_style || {};
      const updatedStyle = {
        vocabulary_level: Math.round((currentStyle.vocabulary_level || 5) * 0.7 + analysis.vocabularyLevel * 0.3),
        formality_preference: analysis.formalityLevel > 6 ? 'professional' : analysis.formalityLevel < 4 ? 'casual' : 'adaptive',
        detail_preference: analysis.responsePreference,
        learning_style: analysis.learningStyle,
        questioning_style: analysis.questioningStyle,
        emotional_expressiveness: Math.round((currentStyle.emotional_expressiveness || 5) * 0.7 + analysis.emotionalExpressiveness * 0.3)
      };
      
      await updateUserMemoryProfile(userId, {
        communication_style: updatedStyle,
        preferred_response_length: analysis.complexityPreference > 7 ? 'long' : analysis.complexityPreference < 4 ? 'short' : 'adaptive'
      });
    }
  } catch (error) {
    console.error('Error updating communication profile:', error);
  }
}
