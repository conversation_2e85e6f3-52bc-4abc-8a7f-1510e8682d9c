// Conversation Intelligence System for OnlyGenyus Group Chats
// Analyzes conversation context, pacing, and determines optimal AI response timing

export interface ConversationContext {
  recentMessages: Array<{
    userId: string
    userName: string
    content: string
    timestamp: Date
    messageType: 'user' | 'assistant'
  }>
  groupMembers: Array<{
    id: string
    name: string
    isActive: boolean
    lastSeen?: Date
  }>
  conversationState: ConversationState
}

export interface ConversationState {
  phase: 'opening' | 'discussion' | 'question_pending' | 'brainstorming' | 'conclusion' | 'waiting'
  lastAIResponse: Date | null
  pendingQuestions: string[]
  activeParticipants: string[]
  conversationTopic: string | null
  energyLevel: 'high' | 'medium' | 'low'
  waitingFor: 'specific_user' | 'any_user' | 'none'
  targetUserId?: string
}

export interface ResponseStrategy {
  shouldRespond: boolean
  responseType: 'immediate' | 'wait_for_specific_user' | 'wait_for_any_user' | 'gentle_prompt'
  waitingFor?: string // user ID or 'any'
  reasoning: string
  suggestedTone: 'enthusiastic' | 'thoughtful' | 'encouraging' | 'patient' | 'summarizing'
  mentionedUsers?: string[] // users mentioned with @
  isDirectedQuestion: boolean
  needsSpecificAnswer: boolean
}

export class ConversationIntelligence {
  
  static analyzeConversationContext(context: ConversationContext): ResponseStrategy {
    const { recentMessages, groupMembers, conversationState } = context
    const lastMessage = recentMessages[0]

    if (!lastMessage) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'No previous messages',
        suggestedTone: 'enthusiastic',
        mentionedUsers: [],
        isDirectedQuestion: false,
        needsSpecificAnswer: false
      }
    }

    // Parse the last message for intelligent context
    const messageContext = this.parseMessageContext(lastMessage, groupMembers)
    const conversationFlow = this.analyzeConversationFlow(recentMessages)

    // Determine response strategy based on intelligent analysis
    return this.determineIntelligentStrategy(messageContext, conversationFlow, context)
  }

  // Parse message for @ mentions, directed questions, and context
  private static parseMessageContext(message: any, groupMembers: any[]) {
    const content = message.content.toLowerCase()

    // Extract @ mentions
    const mentionPattern = /@(\w+)/g
    const mentions = [...content.matchAll(mentionPattern)].map(match => match[1])
    const mentionedUsers = groupMembers.filter(member =>
      mentions.some(mention =>
        member.name.toLowerCase().includes(mention) ||
        member.name.toLowerCase().startsWith(mention)
      )
    ).map(member => member.id)

    // Detect directed questions that need specific answers
    const directedQuestionPatterns = [
      /what do you think about/,
      /how would you handle/,
      /what's your experience with/,
      /have you ever/,
      /what would you do/,
      /how do you feel about/,
      /what's your opinion on/,
      /do you agree that/,
      /what are your thoughts/
    ]

    const isDirectedQuestion = directedQuestionPatterns.some(pattern => pattern.test(content)) ||
                              content.includes('?')

    // Detect questions that need specific user input
    const needsSpecificAnswerPatterns = [
      /tell me about your/,
      /share your experience/,
      /what happened when you/,
      /how did you/,
      /what was it like/,
      /describe your/,
      /walk me through/
    ]

    const needsSpecificAnswer = needsSpecificAnswerPatterns.some(pattern => pattern.test(content))

    // Detect conversation continuers vs enders
    const conversationEnders = [
      'thanks', 'thank you', 'got it', 'makes sense', 'perfect',
      'great', 'awesome', 'sounds good', 'that helps', 'i see'
    ]

    const isConversationEnder = conversationEnders.some(ender => content.includes(ender))

    return {
      mentionedUsers,
      isDirectedQuestion,
      needsSpecificAnswer,
      isConversationEnder,
      hasQuestion: content.includes('?'),
      isPersonalShare: content.includes('i ') || content.includes('my ') || content.includes('me '),
      isOpinionRequest: content.includes('think') || content.includes('opinion') || content.includes('feel')
    }
  }

  // Parse message for @ mentions, directed questions, and context
  private static parseMessageContext(message: ConversationContext['recentMessages'][0], groupMembers: ConversationContext['groupMembers']) {
    const content = message.content.toLowerCase()

    // Extract @ mentions
    const mentionPattern = /@(\w+)/g
    const mentions = [...content.matchAll(mentionPattern)].map(match => match[1])
    const mentionedUserIds = groupMembers
      .filter(member => mentions.some(mention =>
        member.name.toLowerCase().includes(mention) ||
        member.name.toLowerCase().startsWith(mention)
      ))
      .map(member => member.id)

    // Detect question types that need specific answers
    const needsSpecificAnswer = this.detectSpecificAnswerNeeded(content)
    const isDirectedQuestion = this.detectDirectedQuestion(content, mentionedUserIds.length > 0)
    const isPersonalQuestion = this.detectPersonalQuestion(content)
    const isOpenDiscussion = this.detectOpenDiscussion(content)

    return {
      mentionedUsers: mentionedUserIds,
      needsSpecificAnswer,
      isDirectedQuestion,
      isPersonalQuestion,
      isOpenDiscussion,
      hasQuestion: this.endsWithQuestion(content),
      questionType: this.categorizeQuestionType(content),
      conversationIntent: this.detectConversationIntent(content)
    }
  }

  // Detect if question needs a specific person's answer
  private static detectSpecificAnswerNeeded(content: string): boolean {
    const specificPatterns = [
      'what do you think',
      'how do you feel',
      'what\'s your experience',
      'have you ever',
      'what would you do',
      'your opinion',
      'your thoughts',
      'you mentioned',
      'you said',
      'tell us about your',
      'share your'
    ]

    return specificPatterns.some(pattern => content.includes(pattern))
  }

  // Detect if question is directed at specific people
  private static detectDirectedQuestion(content: string, hasMentions: boolean): boolean {
    if (hasMentions) return true

    const directedPatterns = [
      'anyone else',
      'what about you',
      'others think',
      'someone else',
      'different perspective'
    ]

    return directedPatterns.some(pattern => content.includes(pattern))
  }

  // Detect personal/experience questions
  private static detectPersonalQuestion(content: string): boolean {
    const personalPatterns = [
      'your experience',
      'you\'ve done',
      'happened to you',
      'your story',
      'your situation',
      'you dealt with',
      'you handled'
    ]

    return personalPatterns.some(pattern => content.includes(pattern))
  }

  // Detect open discussion starters
  private static detectOpenDiscussion(content: string): boolean {
    const discussionPatterns = [
      'let\'s discuss',
      'what does everyone think',
      'thoughts on',
      'opinions about',
      'brainstorm',
      'ideas for'
    ]

    return discussionPatterns.some(pattern => content.includes(pattern))
  }

  // Categorize question type for better response
  private static categorizeQuestionType(content: string): string {
    if (content.includes('why')) return 'reasoning'
    if (content.includes('how')) return 'process'
    if (content.includes('what if')) return 'hypothetical'
    if (content.includes('should')) return 'advice'
    if (content.includes('experience')) return 'personal'
    if (content.includes('think') || content.includes('opinion')) return 'opinion'
    return 'general'
  }

  // Detect conversation intent
  private static detectConversationIntent(content: string): string {
    if (content.includes('thanks') || content.includes('helpful')) return 'closing'
    if (content.includes('interesting') || content.includes('cool')) return 'engagement'
    if (content.includes('confused') || content.includes('don\'t understand')) return 'clarification'
    if (content.includes('agree') || content.includes('disagree')) return 'opinion'
    return 'continuation'
  }

  // Analyze conversation flow patterns
  private static analyzeConversationFlow(messages: ConversationContext['recentMessages']) {
    const messagePatterns = this.analyzeMessagePatterns(messages)
    const conversationFlow = this.detectConversationFlow(messages)

    return {
      ...messagePatterns,
      flowType: conversationFlow,
      needsResponse: messagePatterns.lastMessageWasQuestion || messagePatterns.multipleUsersActive
    }
  }

  // Determine intelligent response strategy
  private static determineIntelligentStrategy(
    messageContext: any,
    conversationFlow: any,
    context: ConversationContext
  ): ResponseStrategy {
    const { mentionedUsers, needsSpecificAnswer, isDirectedQuestion, isPersonalQuestion, isOpenDiscussion, conversationIntent } = messageContext
    const { recentMessages } = context

    // AI is mentioned - respond immediately
    if (mentionedUsers.length > 0 || messageContext.hasQuestion && !needsSpecificAnswer) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'AI mentioned or general question asked',
        suggestedTone: 'thoughtful',
        mentionedUsers,
        isDirectedQuestion,
        needsSpecificAnswer: false
      }
    }

    // Question needs specific user answer - wait for them
    if (needsSpecificAnswer || isPersonalQuestion) {
      const otherUsers = context.groupMembers.filter(m =>
        m.id !== recentMessages[0].userId && m.isActive
      )

      return {
        shouldRespond: false,
        responseType: 'wait_for_any_user',
        waitingFor: 'any',
        reasoning: 'Question needs personal/specific answer from group members',
        suggestedTone: 'patient',
        mentionedUsers,
        isDirectedQuestion: true,
        needsSpecificAnswer: true
      }
    }

    // Open discussion - let it flow briefly then contribute
    if (isOpenDiscussion) {
      return {
        shouldRespond: false,
        responseType: 'wait_for_any_user',
        waitingFor: 'any',
        reasoning: 'Open discussion started, letting members contribute first',
        suggestedTone: 'encouraging',
        mentionedUsers,
        isDirectedQuestion: false,
        needsSpecificAnswer: false
      }
    }

    // Conversation closing - acknowledge
    if (conversationIntent === 'closing') {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'Conversation appears to be concluding',
        suggestedTone: 'encouraging',
        mentionedUsers,
        isDirectedQuestion: false,
        needsSpecificAnswer: false
      }
    }

    // Default: respond normally
    return {
      shouldRespond: true,
      responseType: 'immediate',
      reasoning: 'Normal conversation flow',
      suggestedTone: 'thoughtful',
      mentionedUsers,
      isDirectedQuestion,
      needsSpecificAnswer
    }
  }

  // Intelligent strategy determination based on context
  private static determineIntelligentStrategy(messageContext: any, conversationFlow: any, context: ConversationContext): ResponseStrategy {
    const { recentMessages, groupMembers } = context
    const lastMessage = recentMessages[0]
    const timeSinceLastAI = context.conversationState.lastAIResponse ?
      Date.now() - context.conversationState.lastAIResponse.getTime() : Infinity

    // IMMEDIATE RESPONSE: Direct questions or mentions
    if (messageContext.isDirectedQuestion || messageContext.mentionedUsers.length > 0) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'Direct question or mention detected',
        suggestedTone: 'thoughtful',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: messageContext.needsSpecificAnswer
      }
    }

    // WAIT FOR SPECIFIC USER: Question needs personal input
    if (messageContext.needsSpecificAnswer && groupMembers.length > 1) {
      const otherMembers = groupMembers.filter(m => m.id !== lastMessage.userId)
      return {
        shouldRespond: false,
        responseType: 'wait_for_specific_user',
        waitingFor: otherMembers[0]?.id || 'any',
        reasoning: 'Question requires personal experience/input from others',
        suggestedTone: 'patient',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: true
      }
    }

    // WAIT FOR ANY USER: Opinion questions in group setting
    if (messageContext.isOpinionRequest && groupMembers.length > 1 && timeSinceLastAI < 60000) {
      return {
        shouldRespond: false,
        responseType: 'wait_for_any_user',
        waitingFor: 'any',
        reasoning: 'Opinion question - waiting for group input',
        suggestedTone: 'encouraging',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: false
      }
    }

    // IMMEDIATE: Conversation ender or single user
    if (messageContext.isConversationEnder || groupMembers.length === 1) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: messageContext.isConversationEnder ? 'Conversation ending' : 'Single user chat',
        suggestedTone: 'thoughtful',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: messageContext.needsSpecificAnswer
      }
    }

    // DEFAULT: Respond normally
    return {
      shouldRespond: true,
      responseType: 'immediate',
      reasoning: 'Normal conversation flow',
      suggestedTone: 'thoughtful',
      mentionedUsers: messageContext.mentionedUsers,
      isDirectedQuestion: messageContext.isDirectedQuestion,
      needsSpecificAnswer: messageContext.needsSpecificAnswer
    }
  }

  // Intelligent strategy determination based on context
  private static determineIntelligentStrategy(messageContext: any, conversationFlow: any, context: ConversationContext): ResponseStrategy {
    const { recentMessages, groupMembers } = context
    const lastMessage = recentMessages[0]
    const timeSinceLastAI = context.conversationState.lastAIResponse ?
      Date.now() - context.conversationState.lastAIResponse.getTime() : Infinity

    // IMMEDIATE RESPONSE: Direct questions or mentions
    if (messageContext.isDirectedQuestion || messageContext.mentionedUsers.length > 0) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'Direct question or mention detected',
        suggestedTone: 'thoughtful',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: messageContext.needsSpecificAnswer
      }
    }

    // WAIT FOR SPECIFIC USER: Question needs personal input
    if (messageContext.needsSpecificAnswer && groupMembers.length > 1) {
      const otherMembers = groupMembers.filter(m => m.id !== lastMessage.userId)
      return {
        shouldRespond: false,
        responseType: 'wait_for_specific_user',
        waitingFor: otherMembers[0]?.id || 'any',
        reasoning: 'Question requires personal experience/input from others',
        suggestedTone: 'patient',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: true
      }
    }

    // WAIT FOR ANY USER: Opinion questions in group setting
    if (messageContext.isOpinionRequest && groupMembers.length > 1 && timeSinceLastAI < 60000) {
      return {
        shouldRespond: false,
        responseType: 'wait_for_any_user',
        waitingFor: 'any',
        reasoning: 'Opinion question - waiting for group input',
        suggestedTone: 'encouraging',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: false
      }
    }

    // IMMEDIATE: Conversation ender or single user
    if (messageContext.isConversationEnder || groupMembers.length === 1) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: messageContext.isConversationEnder ? 'Conversation ending' : 'Single user chat',
        suggestedTone: 'thoughtful',
        mentionedUsers: messageContext.mentionedUsers,
        isDirectedQuestion: messageContext.isDirectedQuestion,
        needsSpecificAnswer: messageContext.needsSpecificAnswer
      }
    }

    // DEFAULT: Respond normally
    return {
      shouldRespond: true,
      responseType: 'immediate',
      reasoning: 'Normal conversation flow',
      suggestedTone: 'thoughtful',
      mentionedUsers: messageContext.mentionedUsers,
      isDirectedQuestion: messageContext.isDirectedQuestion,
      needsSpecificAnswer: messageContext.needsSpecificAnswer
    }
  }

  private static analyzeMessagePatterns(messages: ConversationContext['recentMessages']) {
    const last5Messages = messages.slice(0, 5)
    const userMessages = last5Messages.filter(m => m.messageType === 'user')
    const aiMessages = last5Messages.filter(m => m.messageType === 'assistant')
    
    return {
      recentUserCount: userMessages.length,
      recentAICount: aiMessages.length,
      isRapidFire: this.detectRapidFire(messages),
      hasUnansweredQuestions: this.detectUnansweredQuestions(messages),
      conversationFlow: this.detectConversationFlow(messages),
      lastMessageWasQuestion: this.endsWithQuestion(messages[0]?.content || ''),
      multipleUsersActive: new Set(userMessages.map(m => m.userId)).size > 1
    }
  }

  private static analyzeParticipation(messages: ConversationContext['recentMessages'], members: ConversationContext['groupMembers']) {
    const recentParticipants = new Set(messages.slice(0, 10).map(m => m.userId))
    const silentMembers = members.filter(m => !recentParticipants.has(m.id))
    
    return {
      activeParticipants: recentParticipants.size,
      silentMembers: silentMembers.length,
      needsEncouragement: silentMembers.length > 0 && recentParticipants.size === 1,
      dominantSpeaker: this.findDominantSpeaker(messages.slice(0, 10)),
      participationBalance: this.calculateParticipationBalance(messages.slice(0, 10))
    }
  }

  private static analyzeQuestions(messages: ConversationContext['recentMessages']) {
    const recentQuestions = messages.slice(0, 5).filter(m => 
      m.messageType === 'user' && this.endsWithQuestion(m.content)
    )
    
    const lastAIMessage = messages.find(m => m.messageType === 'assistant')
    const questionsAfterLastAI = lastAIMessage ? 
      messages.slice(0, messages.indexOf(lastAIMessage)).filter(m => 
        m.messageType === 'user' && this.endsWithQuestion(m.content)
      ) : recentQuestions

    return {
      pendingQuestions: questionsAfterLastAI.length,
      questionTypes: this.categorizeQuestions(recentQuestions),
      needsDirectResponse: questionsAfterLastAI.length > 0,
      lastQuestionAsker: recentQuestions[0]?.userId
    }
  }

  private static determineResponseStrategy(analysis: any): ResponseStrategy {
    const {
      messageAnalysis,
      participationAnalysis,
      questionAnalysis,
      timeSinceLastMessage,
      timeSinceLastAI,
      conversationState,
      lastMessage
    } = analysis

    // IMMEDIATE RESPONSE CONDITIONS
    if (questionAnalysis.needsDirectResponse && timeSinceLastMessage < 30000) {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'Direct question needs immediate response',
        suggestedTone: 'thoughtful'
      }
    }

    // AI DOMINATION PREVENTION
    if (messageAnalysis.recentAICount >= 2 && !questionAnalysis.needsDirectResponse) {
      return {
        shouldRespond: false,
        responseType: 'wait_for_input',
        waitDuration: 60,
        reasoning: 'AI has responded recently, waiting for more user input',
        suggestedTone: 'patient'
      }
    }

    // RAPID FIRE CONVERSATION
    if (messageAnalysis.isRapidFire && messageAnalysis.multipleUsersActive) {
      return {
        shouldRespond: false,
        responseType: 'delayed',
        waitDuration: 15,
        reasoning: 'Users are in rapid discussion, letting them continue',
        suggestedTone: 'encouraging'
      }
    }

    // ENCOURAGE SILENT MEMBERS
    if (participationAnalysis.needsEncouragement && timeSinceLastAI > 120000) {
      return {
        shouldRespond: true,
        responseType: 'gentle_prompt',
        reasoning: 'Encouraging silent members to participate',
        suggestedTone: 'encouraging'
      }
    }

    // CONVERSATION STALLED
    if (timeSinceLastMessage > 300000 && conversationState.phase !== 'conclusion') {
      return {
        shouldRespond: true,
        responseType: 'immediate',
        reasoning: 'Conversation has stalled, re-engaging',
        suggestedTone: 'enthusiastic'
      }
    }

    // THOUGHTFUL PAUSE
    if (messageAnalysis.conversationFlow === 'deep_discussion' && timeSinceLastMessage < 60000) {
      return {
        shouldRespond: false,
        responseType: 'delayed',
        waitDuration: 30,
        reasoning: 'Allowing time for thoughtful responses in deep discussion',
        suggestedTone: 'thoughtful'
      }
    }

    // DEFAULT: RESPOND NORMALLY
    return {
      shouldRespond: true,
      responseType: 'immediate',
      reasoning: 'Normal conversation flow',
      suggestedTone: 'thoughtful'
    }
  }

  // Helper methods
  private static detectRapidFire(messages: ConversationContext['recentMessages']): boolean {
    const last3Messages = messages.slice(0, 3)
    if (last3Messages.length < 3) return false
    
    const timeSpan = last3Messages[0].timestamp.getTime() - last3Messages[2].timestamp.getTime()
    return timeSpan < 60000 // 3 messages in under 1 minute
  }

  private static detectUnansweredQuestions(messages: ConversationContext['recentMessages']): boolean {
    const lastAIIndex = messages.findIndex(m => m.messageType === 'assistant')
    const messagesSinceAI = lastAIIndex === -1 ? messages : messages.slice(0, lastAIIndex)
    
    return messagesSinceAI.some(m => this.endsWithQuestion(m.content))
  }

  private static detectConversationFlow(messages: ConversationContext['recentMessages']): string {
    const recentContent = messages.slice(0, 5).map(m => m.content.toLowerCase()).join(' ')
    
    if (recentContent.includes('think') || recentContent.includes('opinion') || recentContent.includes('feel')) {
      return 'deep_discussion'
    }
    if (recentContent.includes('idea') || recentContent.includes('what if') || recentContent.includes('maybe')) {
      return 'brainstorming'
    }
    if (recentContent.includes('thanks') || recentContent.includes('great') || recentContent.includes('helpful')) {
      return 'positive_feedback'
    }
    
    return 'general'
  }

  private static endsWithQuestion(content: string): boolean {
    const trimmed = content.trim()
    return trimmed.endsWith('?') || 
           trimmed.toLowerCase().startsWith('what') ||
           trimmed.toLowerCase().startsWith('how') ||
           trimmed.toLowerCase().startsWith('why') ||
           trimmed.toLowerCase().startsWith('when') ||
           trimmed.toLowerCase().startsWith('where') ||
           trimmed.toLowerCase().startsWith('who')
  }

  private static findDominantSpeaker(messages: ConversationContext['recentMessages']): string | null {
    const userCounts = messages.reduce((acc, msg) => {
      if (msg.messageType === 'user') {
        acc[msg.userId] = (acc[msg.userId] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const entries = Object.entries(userCounts)
    if (entries.length === 0) return null

    const [dominantUser, count] = entries.reduce((max, current) => 
      current[1] > max[1] ? current : max
    )

    // Only consider dominant if they have significantly more messages
    const totalMessages = Object.values(userCounts).reduce((sum, count) => sum + count, 0)
    return count > totalMessages * 0.6 ? dominantUser : null
  }

  private static calculateParticipationBalance(messages: ConversationContext['recentMessages']): number {
    const userCounts = messages.reduce((acc, msg) => {
      if (msg.messageType === 'user') {
        acc[msg.userId] = (acc[msg.userId] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const counts = Object.values(userCounts)
    if (counts.length <= 1) return 1

    const avg = counts.reduce((sum, count) => sum + count, 0) / counts.length
    const variance = counts.reduce((sum, count) => sum + Math.pow(count - avg, 2), 0) / counts.length
    
    return 1 / (1 + variance) // Higher score = more balanced
  }

  private static categorizeQuestions(questions: ConversationContext['recentMessages']): string[] {
    return questions.map(q => {
      const content = q.content.toLowerCase()
      if (content.includes('what do you think') || content.includes('opinion')) return 'opinion'
      if (content.includes('how') && content.includes('do')) return 'process'
      if (content.includes('why')) return 'reasoning'
      if (content.includes('what if')) return 'hypothetical'
      return 'general'
    })
  }

  // Generate context-aware system prompt additions
  static generateContextPrompt(strategy: ResponseStrategy, context: ConversationContext): string {
    const { suggestedTone, reasoning, responseType } = strategy
    
    let prompt = `\n\nCONVERSATION CONTEXT:\n`
    prompt += `- Current situation: ${reasoning}\n`
    prompt += `- Suggested tone: ${suggestedTone}\n`
    prompt += `- Response type: ${responseType}\n`

    if (responseType === 'gentle_prompt') {
      prompt += `- IMPORTANT: Gently encourage silent members to participate without being pushy\n`
    }
    
    if (responseType === 'wait_for_input') {
      prompt += `- IMPORTANT: Keep response brief and leave clear space for user input\n`
    }

    if (context.conversationState.waitingFor === 'specific_user') {
      const targetUser = context.groupMembers.find(m => m.id === context.conversationState.targetUserId)
      prompt += `- WAITING FOR: Specifically waiting for ${targetUser?.name} to respond\n`
    }

    return prompt
  }
}
