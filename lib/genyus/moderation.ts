import OpenAI from 'openai';

export interface ModerationResult {
  ok: boolean;
  flagged: boolean;
  categories?: Record<string, boolean>;
  categoryScores?: Record<string, number>;
  error?: string;
}

export async function moderateContent(text: string): Promise<ModerationResult> {
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });

    const response = await openai.moderations.create({
      model: "omni-moderation-latest",
      input: text,
    });

    const result = response.results[0];
    
    return {
      ok: !result.flagged,
      flagged: result.flagged,
      categories: result.categories,
      categoryScores: result.category_scores,
    };

  } catch (error) {
    console.error('Moderation error:', error);
    
    // If moderation fails, err on the side of caution but don't block
    return {
      ok: true, // Allow content through if moderation service is down
      flagged: false,
      error: error instanceof Error ? error.message : 'Unknown moderation error',
    };
  }
}

// Check if content should be blocked based on moderation
export function shouldBlockContent(moderation: ModerationResult): boolean {
  if (!moderation.flagged) return false;
  
  // Block high-confidence harmful content
  const highRiskCategories = [
    'hate',
    'hate/threatening', 
    'harassment/threatening',
    'self-harm',
    'self-harm/intent',
    'sexual/minors',
    'violence',
    'violence/graphic'
  ];

  if (moderation.categories && moderation.categoryScores) {
    for (const category of highRiskCategories) {
      if (moderation.categories[category] && moderation.categoryScores[category] > 0.8) {
        return true;
      }
    }
  }

  return false;
}

// Generate safe refusal message
export function generateRefusalMessage(moderation: ModerationResult): string {
  const flaggedCategories = moderation.categories 
    ? Object.keys(moderation.categories).filter(key => moderation.categories![key])
    : [];

  if (flaggedCategories.includes('hate') || flaggedCategories.includes('harassment')) {
    return "I can't provide responses that contain harmful or harassing content. Please rephrase your question in a respectful way.";
  }
  
  if (flaggedCategories.includes('violence')) {
    return "I can't provide information that could promote violence or harm. Please ask about something else.";
  }
  
  if (flaggedCategories.includes('sexual')) {
    return "I can't provide responses with sexual content. Please ask about a different topic.";
  }
  
  if (flaggedCategories.includes('self-harm')) {
    return "I'm concerned about your wellbeing. If you're having thoughts of self-harm, please reach out to a mental health professional or crisis helpline.";
  }

  // Generic refusal
  return "I can't provide a response to that question. Please try asking about something else.";
}
