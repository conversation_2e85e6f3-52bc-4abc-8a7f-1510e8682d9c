/**
 * Word counting function for Genyus billing
 * Bills only for what the user sees (moderator's final answer)
 * Must be robust, fast, Unicode-safe, and consistent
 */

export function countChargeableWords(markdown: string): number {
  // 1) Remove fenced code blocks ```...```
  const noCode = markdown.replace(/```[\s\S]*?```/g, " ");

  // 2) Collapse whitespace
  const collapsed = noCode.replace(/\s+/g, " ").trim();

  if (!collapsed) return 0;

  // 3) Prefer Intl.Segmenter for true word boundaries
  //    Falls back to regex if not available in edge runtime
  // @ts-ignore
  if (typeof Intl !== "undefined" && (Intl as any).Segmenter) {
    // @ts-ignore
    const seg = new (Intl as any).Segmenter("en", { granularity: "word" });
    let count = 0;
    for (const { isWordLike } of seg.segment(collapsed)) {
      if (isWordLike) count++;
    }
    return count;
  }

  // 4) Fallback: simple word matcher (compatible with all environments)
  const words = collapsed.match(/[a-zA-Z0-9\u00C0-\u017F\u0100-\u024F\u1E00-\u1EFF''-]+/g);
  return words ? words.length : 0;
}

/**
 * Estimate word count for input validation
 * Used to prevent users from submitting extremely long questions
 */
export function estimateInputWords(text: string): number {
  const trimmed = text.trim();
  if (!trimmed) return 0;
  
  // Simple word count for input validation
  return trimmed.split(/\s+/).length;
}

/**
 * Format word count for display
 */
export function formatWordCount(count: number): string {
  if (count < 1000) {
    return count.toString();
  } else if (count < 1000000) {
    return `${(count / 1000).toFixed(1)}K`;
  } else {
    return `${(count / 1000000).toFixed(1)}M`;
  }
}

/**
 * Calculate estimated cost based on word count
 * This is for display purposes only
 */
export function estimateCost(wordCount: number): number {
  // Rough estimate: $0.001 per word (adjust based on actual costs)
  return wordCount * 0.001;
}
