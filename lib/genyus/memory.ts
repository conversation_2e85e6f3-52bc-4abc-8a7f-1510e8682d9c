import { createClient } from "@supabase/supabase-js";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export async function getConversationHistory(
  userId: string, 
  limit: number = 10
): Promise<ConversationMessage[]> {
  const supabase = createSupabaseServiceClient();
  
  try {
    // Get recent conversation history from genyus_requests and genyus_answers
    const { data: requests, error: reqError } = await supabase
      .from('genyus_requests')
      .select(`
        id,
        question,
        created_at,
        genyus_answers!inner(final_text, created_at)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (reqError) {
      console.error('Error fetching conversation history:', reqError);
      return [];
    }

    // Convert to conversation format
    const messages: ConversationMessage[] = [];
    
    for (const request of requests || []) {
      const answer = request.genyus_answers[0];
      if (answer) {
        // Add user message
        messages.push({
          role: 'user',
          content: request.question,
          timestamp: new Date(request.created_at)
        });
        
        // Add assistant message
        messages.push({
          role: 'assistant',
          content: answer.final_text,
          timestamp: new Date(answer.created_at)
        });
      }
    }

    // Sort by timestamp (oldest first for context)
    return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
  } catch (error) {
    console.error('Error in getConversationHistory:', error);
    return [];
  }
}

export function buildContextualPrompt(
  currentQuestion: string,
  history: ConversationMessage[]
): Array<{role: string, content: string}> {
  const messages = [];
  
  // Add recent history (last 6 messages to keep context manageable)
  const recentHistory = history.slice(-6);
  
  for (const msg of recentHistory) {
    messages.push({
      role: msg.role,
      content: msg.content
    });
  }
  
  // Add current question
  messages.push({
    role: 'user',
    content: currentQuestion
  });
  
  return messages;
}

export function shouldUseContext(history: ConversationMessage[]): boolean {
  // Use context if there's recent conversation history
  return history.length > 0;
}
