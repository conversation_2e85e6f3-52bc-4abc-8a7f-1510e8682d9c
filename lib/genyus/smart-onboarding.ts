import { createClient } from "@supabase/supabase-js";
import { updateUserMemoryProfile } from "./enhanced-memory";

function createSupabaseServiceClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );
}

export interface OnboardingInsight {
  category: 'communication_style' | 'interests' | 'goals' | 'expertise' | 'preferences' | 'context';
  key: string;
  value: any;
  confidence: number; // 0-1
  extractedFrom: string; // The conversation that revealed this
}

export class SmartOnboardingEngine {
  private supabase = createSupabaseServiceClient();

  // Analyze conversation for onboarding insights
  async extractOnboardingInsights(
    userId: string,
    userMessage: string,
    assistantResponse: string,
    interactionCount: number
  ): Promise<OnboardingInsight[]> {
    
    const insights: OnboardingInsight[] = [];
    const lowerMessage = userMessage.toLowerCase();
    
    // Extract communication style insights
    insights.push(...this.extractCommunicationStyle(userMessage));
    
    // Extract interests and expertise
    insights.push(...this.extractInterestsAndExpertise(userMessage));
    
    // Extract goals and projects
    insights.push(...this.extractGoalsAndProjects(userMessage));
    
    // Extract personal context
    insights.push(...this.extractPersonalContext(userMessage));
    
    // Extract preferences from how they ask questions
    insights.push(...this.extractPreferences(userMessage, assistantResponse));
    
    // Store insights in user memory
    await this.storeOnboardingInsights(userId, insights);
    
    return insights;
  }

  // Generate smart follow-up questions based on what we've learned
  generateSmartFollowUps(
    userMessage: string,
    currentInsights: OnboardingInsight[],
    interactionCount: number
  ): string[] {
    
    const followUps: string[] = [];
    const hasInterests = currentInsights.some(i => i.category === 'interests');
    const hasGoals = currentInsights.some(i => i.category === 'goals');
    const hasExpertise = currentInsights.some(i => i.category === 'expertise');
    
    // Early conversations - gather foundational info
    if (interactionCount <= 2) {
      if (!hasInterests && this.mentionsWork(userMessage)) {
        followUps.push("What's the most exciting project you're working on right now?");
      }
      
      if (!hasGoals && this.mentionsChallenge(userMessage)) {
        followUps.push("What would success look like for you with this?");
      }
    }
    
    // Mid onboarding - deepen understanding
    if (interactionCount === 3 || interactionCount === 4) {
      if (hasInterests && !hasExpertise) {
        followUps.push("How long have you been working in this area?");
      }
      
      if (this.mentionsLearning(userMessage)) {
        followUps.push("Do you prefer step-by-step guidance or high-level concepts when learning something new?");
      }
    }
    
    // Later conversations - refine preferences
    if (interactionCount >= 4) {
      if (this.isComplexQuestion(userMessage)) {
        followUps.push("Would you like me to break this down into smaller steps, or do you prefer a comprehensive overview?");
      }
    }
    
    return followUps.slice(0, 1); // Return max 1 follow-up to avoid overwhelming
  }

  private extractCommunicationStyle(message: string): OnboardingInsight[] {
    const insights: OnboardingInsight[] = [];
    
    // Formality level
    const formalIndicators = ['please', 'thank you', 'would you', 'could you'];
    const casualIndicators = ['hey', 'yeah', 'gonna', 'wanna'];
    
    const formalCount = formalIndicators.filter(ind => message.toLowerCase().includes(ind)).length;
    const casualCount = casualIndicators.filter(ind => message.toLowerCase().includes(ind)).length;
    
    if (formalCount > casualCount) {
      insights.push({
        category: 'communication_style',
        key: 'formality_preference',
        value: 'formal',
        confidence: 0.7,
        extractedFrom: message.substring(0, 100)
      });
    } else if (casualCount > formalCount) {
      insights.push({
        category: 'communication_style',
        key: 'formality_preference',
        value: 'casual',
        confidence: 0.7,
        extractedFrom: message.substring(0, 100)
      });
    }
    
    // Detail preference
    if (message.length > 200) {
      insights.push({
        category: 'preferences',
        key: 'detail_preference',
        value: 'detailed',
        confidence: 0.6,
        extractedFrom: 'Long, detailed question'
      });
    } else if (message.length < 50) {
      insights.push({
        category: 'preferences',
        key: 'detail_preference',
        value: 'concise',
        confidence: 0.6,
        extractedFrom: 'Short, concise question'
      });
    }
    
    return insights;
  }

  private extractInterestsAndExpertise(message: string): OnboardingInsight[] {
    const insights: OnboardingInsight[] = [];
    const lowerMessage = message.toLowerCase();
    
    // Technology interests
    const techKeywords = ['programming', 'coding', 'software', 'development', 'python', 'javascript', 'react', 'ai', 'machine learning'];
    const techMatches = techKeywords.filter(keyword => lowerMessage.includes(keyword));
    
    if (techMatches.length > 0) {
      insights.push({
        category: 'interests',
        key: 'technology',
        value: techMatches,
        confidence: 0.8,
        extractedFrom: message.substring(0, 100)
      });
      
      // If they use technical terms correctly, they likely have expertise
      if (techMatches.length > 2) {
        insights.push({
          category: 'expertise',
          key: 'technology_level',
          value: 'intermediate_to_advanced',
          confidence: 0.7,
          extractedFrom: `Used multiple tech terms: ${techMatches.join(', ')}`
        });
      }
    }
    
    // Business interests
    const businessKeywords = ['business', 'marketing', 'sales', 'strategy', 'startup', 'entrepreneur', 'revenue', 'customers'];
    const businessMatches = businessKeywords.filter(keyword => lowerMessage.includes(keyword));
    
    if (businessMatches.length > 0) {
      insights.push({
        category: 'interests',
        key: 'business',
        value: businessMatches,
        confidence: 0.8,
        extractedFrom: message.substring(0, 100)
      });
    }
    
    // Creative interests
    const creativeKeywords = ['design', 'writing', 'creative', 'art', 'music', 'photography', 'video'];
    const creativeMatches = creativeKeywords.filter(keyword => lowerMessage.includes(keyword));
    
    if (creativeMatches.length > 0) {
      insights.push({
        category: 'interests',
        key: 'creative',
        value: creativeMatches,
        confidence: 0.8,
        extractedFrom: message.substring(0, 100)
      });
    }
    
    return insights;
  }

  private extractGoalsAndProjects(message: string): OnboardingInsight[] {
    const insights: OnboardingInsight[] = [];
    const lowerMessage = message.toLowerCase();
    
    // Project mentions
    if (lowerMessage.includes('project') || lowerMessage.includes('working on')) {
      const projectMatch = message.match(/(?:project|working on) ([^.!?]+)/i);
      if (projectMatch) {
        insights.push({
          category: 'goals',
          key: 'current_project',
          value: projectMatch[1].trim(),
          confidence: 0.9,
          extractedFrom: message.substring(0, 100)
        });
      }
    }
    
    // Goal indicators
    const goalKeywords = ['want to', 'trying to', 'goal', 'hoping to', 'planning to', 'need to'];
    const goalMatches = goalKeywords.filter(keyword => lowerMessage.includes(keyword));
    
    if (goalMatches.length > 0) {
      insights.push({
        category: 'goals',
        key: 'has_active_goals',
        value: true,
        confidence: 0.8,
        extractedFrom: `Goal language: ${goalMatches.join(', ')}`
      });
    }
    
    // Learning goals
    if (lowerMessage.includes('learn') || lowerMessage.includes('understand') || lowerMessage.includes('how to')) {
      insights.push({
        category: 'goals',
        key: 'learning_oriented',
        value: true,
        confidence: 0.8,
        extractedFrom: 'Learning-focused question'
      });
    }
    
    return insights;
  }

  private extractPersonalContext(message: string): OnboardingInsight[] {
    const insights: OnboardingInsight[] = [];
    const lowerMessage = message.toLowerCase();
    
    // Work context
    if (lowerMessage.includes('work') || lowerMessage.includes('job') || lowerMessage.includes('company')) {
      insights.push({
        category: 'context',
        key: 'work_related',
        value: true,
        confidence: 0.8,
        extractedFrom: 'Work-related discussion'
      });
    }
    
    // Personal projects
    if (lowerMessage.includes('personal') || lowerMessage.includes('side project') || lowerMessage.includes('hobby')) {
      insights.push({
        category: 'context',
        key: 'personal_projects',
        value: true,
        confidence: 0.8,
        extractedFrom: 'Personal project mention'
      });
    }
    
    // Time sensitivity
    if (lowerMessage.includes('urgent') || lowerMessage.includes('deadline') || lowerMessage.includes('quickly')) {
      insights.push({
        category: 'preferences',
        key: 'time_sensitive',
        value: true,
        confidence: 0.9,
        extractedFrom: 'Time-sensitive language'
      });
    }
    
    return insights;
  }

  private extractPreferences(userMessage: string, assistantResponse: string): OnboardingInsight[] {
    const insights: OnboardingInsight[] = [];
    const lowerMessage = userMessage.toLowerCase();
    
    // Response format preferences
    if (lowerMessage.includes('step by step') || lowerMessage.includes('steps')) {
      insights.push({
        category: 'preferences',
        key: 'response_format',
        value: 'step_by_step',
        confidence: 0.9,
        extractedFrom: 'Requested step-by-step format'
      });
    }
    
    if (lowerMessage.includes('example') || lowerMessage.includes('examples')) {
      insights.push({
        category: 'preferences',
        key: 'learning_style',
        value: 'example_driven',
        confidence: 0.8,
        extractedFrom: 'Requested examples'
      });
    }
    
    if (lowerMessage.includes('briefly') || lowerMessage.includes('summary') || lowerMessage.includes('quick')) {
      insights.push({
        category: 'preferences',
        key: 'response_length',
        value: 'concise',
        confidence: 0.8,
        extractedFrom: 'Requested brief response'
      });
    }
    
    return insights;
  }

  private async storeOnboardingInsights(userId: string, insights: OnboardingInsight[]): Promise<void> {
    if (insights.length === 0) return;
    
    try {
      // Group insights by category for efficient storage
      const groupedInsights: Record<string, any> = {};
      
      insights.forEach(insight => {
        if (!groupedInsights[insight.category]) {
          groupedInsights[insight.category] = {};
        }
        groupedInsights[insight.category][insight.key] = {
          value: insight.value,
          confidence: insight.confidence,
          extractedFrom: insight.extractedFrom
        };
      });
      
      // Update user memory with new insights
      await updateUserMemoryProfile(userId, {
        onboarding_insights: groupedInsights,
        last_insight_update: new Date()
      });
      
    } catch (error) {
      console.error('Error storing onboarding insights:', error);
    }
  }

  // Helper methods
  private mentionsWork(message: string): boolean {
    const workKeywords = ['work', 'job', 'project', 'business', 'company', 'team'];
    return workKeywords.some(keyword => message.toLowerCase().includes(keyword));
  }

  private mentionsChallenge(message: string): boolean {
    const challengeKeywords = ['problem', 'issue', 'challenge', 'difficult', 'stuck', 'help'];
    return challengeKeywords.some(keyword => message.toLowerCase().includes(keyword));
  }

  private mentionsLearning(message: string): boolean {
    const learningKeywords = ['learn', 'understand', 'how to', 'teach', 'explain'];
    return learningKeywords.some(keyword => message.toLowerCase().includes(keyword));
  }

  private isComplexQuestion(message: string): boolean {
    return message.length > 150 || message.split('?').length > 2;
  }
}

export const smartOnboarding = new SmartOnboardingEngine();
