import { RATE_LIMITS } from './config';

// Simple in-memory rate limiter for edge runtime
// TODO: Replace with Redis for production scaling
class MemoryRateLimiter {
  private requests = new Map<string, number[]>();
  
  // Check if user is within rate limits
  isAllowed(userId: string): boolean {
    const now = Date.now();
    const userRequests = this.requests.get(userId) || [];
    
    // Clean up old requests (older than 1 hour)
    const recentRequests = userRequests.filter(timestamp => 
      now - timestamp < 60 * 60 * 1000 // 1 hour
    );
    
    // Check minute limit
    const lastMinuteRequests = recentRequests.filter(timestamp => 
      now - timestamp < 60 * 1000 // 1 minute
    );
    
    if (lastMinuteRequests.length >= RATE_LIMITS.REQUESTS_PER_MINUTE) {
      return false;
    }
    
    // Check hour limit
    if (recentRequests.length >= RATE_LIMITS.REQUESTS_PER_HOUR) {
      return false;
    }
    
    return true;
  }
  
  // Record a request
  recordRequest(userId: string): void {
    const now = Date.now();
    const userRequests = this.requests.get(userId) || [];
    
    // Add current request
    userRequests.push(now);
    
    // Clean up old requests
    const recentRequests = userRequests.filter(timestamp => 
      now - timestamp < 60 * 60 * 1000 // Keep last hour
    );
    
    this.requests.set(userId, recentRequests);
    
    // Cleanup memory periodically
    if (this.requests.size > 10000) {
      this.cleanup();
    }
  }
  
  // Get remaining requests for user
  getRemainingRequests(userId: string): { minute: number; hour: number } {
    const now = Date.now();
    const userRequests = this.requests.get(userId) || [];
    
    const lastMinuteRequests = userRequests.filter(timestamp => 
      now - timestamp < 60 * 1000
    ).length;
    
    const lastHourRequests = userRequests.filter(timestamp => 
      now - timestamp < 60 * 60 * 1000
    ).length;
    
    return {
      minute: Math.max(0, RATE_LIMITS.REQUESTS_PER_MINUTE - lastMinuteRequests),
      hour: Math.max(0, RATE_LIMITS.REQUESTS_PER_HOUR - lastHourRequests),
    };
  }
  
  // Reset limits for user (admin function)
  resetUser(userId: string): void {
    this.requests.delete(userId);
  }
  
  // Cleanup old entries
  private cleanup(): void {
    const now = Date.now();
    const cutoff = now - (60 * 60 * 1000); // 1 hour ago
    
    for (const [userId, timestamps] of this.requests.entries()) {
      const recentRequests = timestamps.filter(timestamp => timestamp > cutoff);
      
      if (recentRequests.length === 0) {
        this.requests.delete(userId);
      } else {
        this.requests.set(userId, recentRequests);
      }
    }
  }
  
  // Get stats
  getStats(): { totalUsers: number; totalRequests: number } {
    let totalRequests = 0;
    for (const timestamps of this.requests.values()) {
      totalRequests += timestamps.length;
    }
    
    return {
      totalUsers: this.requests.size,
      totalRequests,
    };
  }
}

// Global rate limiter instance
const rateLimiter = new MemoryRateLimiter();

// Public API
export function checkRateLimit(userId: string): boolean {
  return rateLimiter.isAllowed(userId);
}

export function recordRequest(userId: string): void {
  rateLimiter.recordRequest(userId);
}

export function getRemainingRequests(userId: string): { minute: number; hour: number } {
  return rateLimiter.getRemainingRequests(userId);
}

export function resetUserRateLimit(userId: string): void {
  rateLimiter.resetUser(userId);
}

export function getRateLimitStats(): { totalUsers: number; totalRequests: number } {
  return rateLimiter.getStats();
}

// Rate limit error response
export function createRateLimitError(): Response {
  return new Response(JSON.stringify({
    error: "Rate limit exceeded",
    message: "Too many requests. Please wait before trying again.",
    retryAfter: 60, // seconds
  }), {
    status: 429,
    headers: {
      "Content-Type": "application/json",
      "Retry-After": "60",
    },
  });
}

// Redis implementation for production (when ready)
/*
import Redis from 'ioredis';

let redis: Redis | null = null;

function getRedis(): Redis {
  if (!redis) {
    redis = new Redis(process.env.UPSTASH_REDIS_REST_URL!, {
      password: process.env.UPSTASH_REDIS_REST_TOKEN,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    });
  }
  return redis;
}

export async function checkRateLimit(userId: string): Promise<boolean> {
  try {
    const redis = getRedis();
    const now = Date.now();
    
    // Use Redis sorted sets for efficient time-based rate limiting
    const minuteKey = `rate_limit:${userId}:minute`;
    const hourKey = `rate_limit:${userId}:hour`;
    
    // Check minute limit
    const minuteCount = await redis.zcount(minuteKey, now - 60000, now);
    if (minuteCount >= RATE_LIMITS.REQUESTS_PER_MINUTE) {
      return false;
    }
    
    // Check hour limit
    const hourCount = await redis.zcount(hourKey, now - 3600000, now);
    if (hourCount >= RATE_LIMITS.REQUESTS_PER_HOUR) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Rate limit check error:', error);
    return true; // Allow on error
  }
}

export async function recordRequest(userId: string): Promise<void> {
  try {
    const redis = getRedis();
    const now = Date.now();
    const requestId = `${now}-${Math.random()}`;
    
    const minuteKey = `rate_limit:${userId}:minute`;
    const hourKey = `rate_limit:${userId}:hour`;
    
    // Add to both time windows
    await Promise.all([
      redis.zadd(minuteKey, now, requestId),
      redis.zadd(hourKey, now, requestId),
      redis.expire(minuteKey, 60),
      redis.expire(hourKey, 3600),
    ]);
    
    // Clean up old entries
    await Promise.all([
      redis.zremrangebyscore(minuteKey, 0, now - 60000),
      redis.zremrangebyscore(hourKey, 0, now - 3600000),
    ]);
  } catch (error) {
    console.error('Rate limit record error:', error);
    // Don't throw - rate limiting is not critical
  }
}
*/
