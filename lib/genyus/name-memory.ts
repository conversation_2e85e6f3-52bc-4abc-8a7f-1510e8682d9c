// Name Learning and Memory System for OnlyGenyus Group Chats
// Automatically learns and remembers user names, preferences, and context

export interface UserProfile {
  id: string
  name: string
  email: string
  bio?: string
  avatar?: string
  joinedAt: Date
  preferredName?: string // What they like to be called
  conversationStyle?: string
  interests?: string[]
  lastActive?: Date
}

export interface GroupMemoryContext {
  groupId: string
  members: UserProfile[]
  conversationHistory: Array<{
    userId: string
    content: string
    timestamp: Date
    messageType: 'user' | 'assistant'
  }>
  namePreferences: Record<string, string> // userId -> preferred name
  relationshipContext: Record<string, string> // userId -> relationship notes
}

export class NameMemorySystem {
  
  // Extract and learn names from user profiles and conversation
  static buildGroupMemory(
    groupId: string,
    memberDetails: any[],
    conversationHistory: any[]
  ): GroupMemoryContext {
    
    const members: UserProfile[] = memberDetails.map(member => ({
      id: member.user_id,
      name: member.users.name || this.extractNameFromEmail(member.users.email),
      email: member.users.email,
      bio: member.users.bio,
      avatar: member.users.avatar,
      joinedAt: new Date(member.joined_at),
      preferredName: this.extractPreferredName(member.users.name, member.users.bio),
      conversationStyle: this.analyzeConversationStyle(member.user_id, conversationHistory),
      interests: this.extractInterests(member.users.bio),
      lastActive: this.getLastActivity(member.user_id, conversationHistory)
    }))

    const namePreferences = this.learnNamePreferences(members, conversationHistory)
    const relationshipContext = this.buildRelationshipContext(members, conversationHistory)

    return {
      groupId,
      members,
      conversationHistory: conversationHistory.map(msg => ({
        userId: msg.user_id,
        content: msg.content,
        timestamp: new Date(msg.created_at),
        messageType: msg.message_type
      })),
      namePreferences,
      relationshipContext
    }
  }

  // Extract name from email if no display name
  private static extractNameFromEmail(email: string): string {
    const localPart = email.split('@')[0]
    // Convert common patterns: john.doe -> John Doe, johnsmith -> John Smith
    return localPart
      .replace(/[._-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .replace(/(\w)([A-Z])/g, '$1 $2') // camelCase -> Camel Case
  }

  // Extract preferred name from display name or bio
  private static extractPreferredName(displayName?: string, bio?: string): string | undefined {
    if (!displayName) return undefined

    // Check for common nickname patterns
    const nicknames = [
      /call me (\w+)/i,
      /goes by (\w+)/i,
      /known as (\w+)/i,
      /"(\w+)"/g, // Quoted nicknames
      /\((\w+)\)/g // Parenthetical nicknames
    ]

    // Check bio first
    if (bio) {
      for (const pattern of nicknames) {
        const match = bio.match(pattern)
        if (match) return match[1]
      }
    }

    // Extract first name from display name
    const firstName = displayName.split(' ')[0]
    return firstName
  }

  // Analyze conversation style from message history
  private static analyzeConversationStyle(userId: string, history: any[]): string {
    const userMessages = history.filter(msg => msg.user_id === userId && msg.message_type === 'user')
    if (userMessages.length === 0) return 'new_member'

    const totalLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0)
    const avgLength = totalLength / userMessages.length

    const hasQuestions = userMessages.some(msg => msg.content.includes('?'))
    const hasEmojis = userMessages.some(msg => /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(msg.content))
    const isFormal = userMessages.some(msg => /please|thank you|would you|could you/i.test(msg.content))

    if (avgLength > 100 && hasQuestions) return 'thoughtful_questioner'
    if (hasEmojis && avgLength < 50) return 'casual_friendly'
    if (isFormal) return 'polite_formal'
    if (avgLength < 30) return 'concise_direct'
    
    return 'balanced_conversationalist'
  }

  // Extract interests from bio
  private static extractInterests(bio?: string): string[] {
    if (!bio) return []

    const interestKeywords = [
      'love', 'enjoy', 'passionate about', 'interested in', 'hobby', 'hobbies',
      'fan of', 'into', 'enthusiast', 'work in', 'study', 'learning'
    ]

    const interests: string[] = []
    const lowerBio = bio.toLowerCase()

    // Look for interest patterns
    interestKeywords.forEach(keyword => {
      const index = lowerBio.indexOf(keyword)
      if (index !== -1) {
        // Extract the next few words after the keyword
        const afterKeyword = bio.substring(index + keyword.length).trim()
        const words = afterKeyword.split(/[.,!?;]|and|&/)[0].trim()
        if (words.length > 0 && words.length < 50) {
          interests.push(words)
        }
      }
    })

    return interests.slice(0, 3) // Limit to top 3 interests
  }

  // Get last activity time for user
  private static getLastActivity(userId: string, history: any[]): Date | undefined {
    const userMessages = history.filter(msg => msg.user_id === userId)
    if (userMessages.length === 0) return undefined
    
    return new Date(userMessages[0].created_at) // Most recent first
  }

  // Learn name preferences from conversation
  private static learnNamePreferences(members: UserProfile[], history: any[]): Record<string, string> {
    const preferences: Record<string, string> = {}

    // Start with preferred names from profiles
    members.forEach(member => {
      if (member.preferredName) {
        preferences[member.id] = member.preferredName
      }
    })

    // Learn from conversation patterns
    history.forEach(msg => {
      if (msg.message_type === 'user') {
        // Look for self-introductions: "I'm John" or "Call me Johnny"
        const introPatterns = [
          /i'm (\w+)/i,
          /call me (\w+)/i,
          /my name is (\w+)/i,
          /i go by (\w+)/i
        ]

        introPatterns.forEach(pattern => {
          const match = msg.content.match(pattern)
          if (match) {
            preferences[msg.user_id] = match[1]
          }
        })
      }
    })

    return preferences
  }

  // Build relationship context between members
  private static buildRelationshipContext(members: UserProfile[], history: any[]): Record<string, string> {
    const context: Record<string, string> = {}

    members.forEach(member => {
      const notes: string[] = []

      // Add bio context
      if (member.bio) {
        notes.push(`Bio: ${member.bio.substring(0, 100)}`)
      }

      // Add conversation style
      if (member.conversationStyle) {
        notes.push(`Style: ${member.conversationStyle.replace(/_/g, ' ')}`)
      }

      // Add interests
      if (member.interests && member.interests.length > 0) {
        notes.push(`Interests: ${member.interests.join(', ')}`)
      }

      // Add join context
      const joinedRecently = Date.now() - member.joinedAt.getTime() < 7 * 24 * 60 * 60 * 1000
      if (joinedRecently) {
        notes.push('Recently joined the group')
      }

      context[member.id] = notes.join(' | ')
    })

    return context
  }

  // Generate name-aware system prompt addition
  static generateNamePrompt(memory: GroupMemoryContext): string {
    const memberIntros = memory.members.map(member => {
      const preferredName = memory.namePreferences[member.id] || member.name
      const context = memory.relationshipContext[member.id]
      
      return `- ${preferredName} (${member.email}): ${context || 'Group member'}`
    }).join('\n')

    return `
IMPORTANT - GROUP MEMBER INFORMATION:
You are chatting with these specific people. Use their names naturally in conversation:

${memberIntros}

NAME USAGE GUIDELINES:
- Always use their preferred names when addressing them directly
- Reference people by name when responding to their specific comments
- Use names to create personal connections: "Great point, Sarah!" or "Mike, you mentioned earlier..."
- When asking follow-up questions, use names: "What do you think about that, Alex?"
- Remember their interests and conversation styles when responding
- Make each person feel recognized and valued in the group

CONVERSATION MEMORY:
- Remember what each person has shared in this conversation
- Reference their previous contributions when relevant
- Build on their ideas and connect different members' thoughts
- Acknowledge when someone hasn't spoken yet: "We haven't heard from Lisa yet - what's your take?"

Be warm, personal, and make everyone feel included by using their names thoughtfully.`
  }

  // Update memory with new message
  static updateMemory(memory: GroupMemoryContext, newMessage: any): GroupMemoryContext {
    // Add new message to history
    memory.conversationHistory.unshift({
      userId: newMessage.user_id,
      content: newMessage.content,
      timestamp: new Date(),
      messageType: 'user'
    })

    // Update last activity
    const member = memory.members.find(m => m.id === newMessage.user_id)
    if (member) {
      member.lastActive = new Date()
    }

    // Learn any new name preferences from the message
    const newPreferences = this.learnNamePreferences(memory.members, [newMessage])
    Object.assign(memory.namePreferences, newPreferences)

    return memory
  }
}
