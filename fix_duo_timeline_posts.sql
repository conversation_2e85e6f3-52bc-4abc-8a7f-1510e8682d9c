-- Fix Duo Timeline Posts Issue
-- This script creates the missing trigger to add duo posts to timeline_posts when completed

-- Function to create timeline post when duo is completed
CREATE OR REPLACE FUNCTION create_timeline_post_for_duo()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Only create timeline post when status changes to 'completed'
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        INSERT INTO public.timeline_posts (
            user_id,
            content_type,
            title,
            description,
            is_free,
            is_hidden,
            duo_post_id,
            created_at
        ) VALUES (
            NEW.initiator_user_id,
            'duo',
            COALESCE(NEW.title, 'OnlyDuo'),
            COALESCE(NEW.body, 'A collaborative video creation'),
            true, -- Duos are free by default
            false, -- Show on timeline when completed
            NEW.id,
            NEW.updated_at
        )
        ON CONFLICT (duo_post_id) DO UPDATE SET
            is_hidden = false,
            updated_at = NEW.updated_at;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for duo posts
DROP TRIGGER IF EXISTS trigger_duo_timeline_post ON duo_posts;
CREATE TRIGGER trigger_duo_timeline_post
    AFTER INSERT OR UPDATE ON duo_posts
    FOR EACH ROW
    EXECUTE FUNCTION create_timeline_post_for_duo();

-- Backfill existing completed duos to timeline_posts
INSERT INTO timeline_posts (
    user_id,
    content_type,
    title,
    description,
    is_free,
    is_hidden,
    duo_post_id,
    created_at,
    updated_at
)
SELECT 
    dp.initiator_user_id,
    'duo',
    COALESCE(dp.title, 'OnlyDuo'),
    COALESCE(dp.body, 'A collaborative video creation'),
    true,
    false,
    dp.id,
    dp.created_at,
    dp.updated_at
FROM duo_posts dp
WHERE dp.status = 'completed'
AND NOT EXISTS (
    SELECT 1 FROM timeline_posts tp 
    WHERE tp.duo_post_id = dp.id
);
