'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface OnboardingModalProps {
  isOpen: boolean
  onClose: () => void
  userName?: string
  userId?: string
  source?: 'group_chat' | 'signup' | 'manual'
}

type OnboardingStep = 'welcome' | 'adventure' | 'create' | 'discover' | 'monetize' | 'complete'

export function SocialMedia2OnboardingModal({ 
  isOpen, 
  onClose, 
  userName = 'Creator',
  userId,
  source = 'signup'
}: OnboardingModalProps) {
  const [step, setStep] = useState<OnboardingStep>('welcome')
  const [selectedAdventure, setSelectedAdventure] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [completedActions, setCompletedActions] = useState<string[]>([])
  
  const supabase = createSupabaseClient()

  // Calculate progress
  useEffect(() => {
    const steps = ['welcome', 'adventure', 'create', 'discover', 'monetize', 'complete']
    const currentIndex = steps.indexOf(step)
    setProgress((currentIndex / (steps.length - 1)) * 100)
  }, [step])

  const trackAction = async (action: string) => {
    if (!userId) return
    
    try {
      await supabase.from('user_onboarding_actions').insert({
        user_id: userId,
        action,
        source,
        completed_at: new Date().toISOString()
      })
      
      setCompletedActions(prev => [...prev, action])
    } catch (error) {
      console.error('Error tracking onboarding action:', error)
    }
  }

  const handleAdventureSelect = (adventure: string) => {
    setSelectedAdventure(adventure)
    trackAction(`selected_${adventure}`)
    
    // Auto-advance after selection
    setTimeout(() => setStep('create'), 500)
  }

  const handleCreateAction = (action: string) => {
    trackAction(`create_${action}`)
    
    // Simulate action completion
    setTimeout(() => {
      setCompletedActions(prev => [...prev, action])
      setStep('discover')
    }, 1000)
  }

  const handleSkip = () => {
    trackAction('onboarding_skipped')
    onClose()
  }

  const handleComplete = () => {
    trackAction('onboarding_completed')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        
        {/* Progress Bar */}
        <div className="h-2 bg-gray-100 rounded-t-3xl">
          <div 
            className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-t-3xl transition-all duration-500"
            style={{ width: `${progress}%` }}
          />
        </div>

        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Welcome to Social Media 2.0
              </h2>
              <p className="text-gray-600 mt-1">OnlyDiary's cutting-edge tools await</p>
            </div>
            <button
              onClick={handleSkip}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'welcome' && (
            <div className="text-center space-y-6">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-3xl">🚀</span>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {source === 'group_chat' 
                    ? `Great conversation, ${userName}! 🎉`
                    : `Hey ${userName}! Ready to lead the future?`
                  }
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed">
                  {source === 'group_chat' 
                    ? "You just experienced OnlyGenyus - our intelligent AI with memory. That's just one cutting-edge tool in our social tech hub."
                    : "OnlyDiary gives you cutting-edge tools to create, connect, and lead in ways that weren't possible before."
                  }
                </p>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-2xl">
                <h4 className="font-bold text-gray-900 mb-3">🔥 What makes us 2.0:</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                    <span>AI-powered conversations</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    <span>9-second audio stories</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                    <span>Instant book publishing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    <span>Creator monetization</span>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setStep('adventure')}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-4 rounded-2xl font-bold text-lg hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-[1.02]"
              >
                Let's explore the future →
              </button>
            </div>
          )}

          {step === 'adventure' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Choose Your Adventure</h3>
                <p className="text-gray-600">What sounds most exciting right now? (Don't worry, you can try everything!)</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { id: 'audio', icon: '🎵', title: 'Create 9-second audio stories', desc: 'Revolutionary micro-storytelling' },
                  { id: 'write', icon: '✍️', title: 'Write and sell access', desc: 'Monetize your thoughts instantly' },
                  { id: 'books', icon: '📚', title: 'Publish books instantly', desc: 'Turn ideas into published works' },
                  { id: 'ai', icon: '🤖', title: 'AI with memory', desc: 'Chat with AI that remembers everything' },
                  { id: 'community', icon: '👥', title: 'Build reader communities', desc: 'Grow your following & income' },
                  { id: 'discover', icon: '🔥', title: 'Discover cutting-edge creators', desc: 'Find tomorrow\'s innovators' }
                ].map((adventure) => (
                  <button
                    key={adventure.id}
                    onClick={() => handleAdventureSelect(adventure.id)}
                    className={`p-4 border-2 rounded-2xl text-left transition-all transform hover:scale-[1.02] ${
                      selectedAdventure === adventure.id
                        ? 'border-purple-500 bg-purple-50 ring-2 ring-purple-200'
                        : 'border-gray-200 hover:border-purple-300 hover:bg-purple-25'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">{adventure.icon}</span>
                      <div>
                        <h4 className="font-bold text-gray-900">{adventure.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{adventure.desc}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {step === 'create' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Let's Create Something Right Now</h3>
                <p className="text-gray-600">Don't worry about perfection - this is your playground for innovation!</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => handleCreateAction('audio')}
                  className="p-6 border-2 border-gray-200 rounded-2xl hover:border-purple-300 hover:bg-purple-25 transition-all text-center group"
                >
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                    <span className="text-xl">🎵</span>
                  </div>
                  <h4 className="font-bold text-gray-900">Record 9-second audio</h4>
                  <p className="text-sm text-gray-600 mt-1">Quick & powerful</p>
                </button>

                <button
                  onClick={() => handleCreateAction('write')}
                  className="p-6 border-2 border-gray-200 rounded-2xl hover:border-blue-300 hover:bg-blue-25 transition-all text-center group"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                    <span className="text-xl">✍️</span>
                  </div>
                  <h4 className="font-bold text-gray-900">Write first entry</h4>
                  <p className="text-sm text-gray-600 mt-1">Share your thoughts</p>
                </button>

                <button
                  onClick={() => handleCreateAction('ai')}
                  className="p-6 border-2 border-gray-200 rounded-2xl hover:border-green-300 hover:bg-green-25 transition-all text-center group"
                >
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                    <span className="text-xl">🤖</span>
                  </div>
                  <h4 className="font-bold text-gray-900">Ask AI anything</h4>
                  <p className="text-sm text-gray-600 mt-1">Start chatting</p>
                </button>
              </div>

              <div className="text-center">
                <button
                  onClick={() => setStep('discover')}
                  className="text-purple-600 hover:text-purple-700 font-medium"
                >
                  I'll create later, show me more →
                </button>
              </div>
            </div>
          )}

          {step === 'discover' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Find Your Tribe of Innovators</h3>
                <p className="text-gray-600">Discover creators who are building the future</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-6 border-2 border-gray-200 rounded-2xl hover:border-purple-300 hover:bg-purple-25 transition-all">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🧠</span>
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2">ADHD Creators</h4>
                  <p className="text-sm text-gray-600">Productivity hacks, innovation tips, and authentic stories</p>
                </div>

                <div className="p-6 border-2 border-gray-200 rounded-2xl hover:border-blue-300 hover:bg-blue-25 transition-all">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🚀</span>
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2">Tech Enthusiasts</h4>
                  <p className="text-sm text-gray-600">Building tomorrow's tools and platforms</p>
                </div>

                <div className="p-6 border-2 border-gray-200 rounded-2xl hover:border-green-300 hover:bg-green-25 transition-all">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">✍️</span>
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2">Experimental Writers</h4>
                  <p className="text-sm text-gray-600">New formats, bold ideas, authentic expression</p>
                </div>

                <div className="p-6 border-2 border-gray-200 rounded-2xl hover:border-orange-300 hover:bg-orange-25 transition-all">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl">🎵</span>
                  </div>
                  <h4 className="font-bold text-gray-900 mb-2">Audio Storytellers</h4>
                  <p className="text-sm text-gray-600">Pushing boundaries with 9-second stories</p>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setStep('monetize')}
                  className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-2xl font-bold hover:from-purple-700 hover:to-blue-700 transition-all"
                >
                  Explore communities →
                </button>
                <button
                  onClick={() => setStep('monetize')}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Skip
                </button>
              </div>
            </div>
          )}

          {step === 'monetize' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Turn Creativity Into Income</h3>
                <p className="text-gray-600">OnlyDiary makes monetization simple and powerful</p>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-2xl">
                <h4 className="font-bold text-gray-900 mb-4">💰 Multiple Revenue Streams:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-sm">📝</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">Monthly Subscriptions</h5>
                      <p className="text-sm text-gray-600">Set your rate, build recurring income</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm">📚</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">Book & Recipe Sales</h5>
                      <p className="text-sm text-gray-600">Sell individual works instantly</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-sm">💝</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">Donations</h5>
                      <p className="text-sm text-gray-600">Accept support from readers</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-sm">🎵</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">Premium Content</h5>
                      <p className="text-sm text-gray-600">Mark posts as paid access</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setStep('complete')}
                  className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 rounded-2xl font-bold hover:from-green-700 hover:to-blue-700 transition-all"
                >
                  Set up monetization →
                </button>
                <button
                  onClick={() => setStep('complete')}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
          )}

          {step === 'complete' && (
            <div className="text-center space-y-6">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-3xl">🎉</span>
              </div>

              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Welcome to the Future!</h3>
                <p className="text-gray-600 text-lg leading-relaxed">
                  You're now part of social media 2.0. Start creating, connecting, and leading with cutting-edge tools.
                </p>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-2xl">
                <h4 className="font-bold text-gray-900 mb-3">🚀 Quick Start Ideas:</h4>
                <div className="text-left space-y-2 text-sm text-gray-700">
                  <div>• Record your first 9-second audio story</div>
                  <div>• Write and sell access to your thoughts</div>
                  <div>• Chat with AI that remembers everything</div>
                  <div>• Publish a book from your ideas</div>
                  <div>• Set up your creator monetization</div>
                </div>
              </div>

              <button
                onClick={handleComplete}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-4 rounded-2xl font-bold text-lg hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-[1.02]"
              >
                Start building the future →
              </button>
            </div>
          )}

        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-100 bg-gray-50 rounded-b-3xl">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Step {['welcome', 'adventure', 'create', 'discover', 'monetize', 'complete'].indexOf(step) + 1} of 6
            </div>
            <div className="flex space-x-3">
              {step !== 'welcome' && (
                <button
                  onClick={() => {
                    const steps: OnboardingStep[] = ['welcome', 'adventure', 'create', 'discover', 'monetize', 'complete']
                    const currentIndex = steps.indexOf(step)
                    if (currentIndex > 0) setStep(steps[currentIndex - 1])
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  ← Back
                </button>
              )}
              <button
                onClick={handleSkip}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Skip for now
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
