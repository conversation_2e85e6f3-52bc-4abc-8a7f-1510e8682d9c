'use client'

import { useEffect } from 'react'

export function ServiceWorkerRegistration() {
  useEffect(() => {
    // Skip service worker in development or non-secure contexts
    const isProduction = process.env.NODE_ENV === 'production'
    const isSecureContext = window.location.protocol === 'https:' || window.location.hostname === 'localhost'
    
    if (!('serviceWorker' in navigator)) {
      console.log('Service Workers not supported in this browser')
      return
    }

    if (!isSecureContext && isProduction) {
      console.log('Service Workers require secure context (HTTPS)')
      return
    }

    // For development, we'll skip service worker registration
    if (!isProduction) {
      console.log('Service Worker registration skipped in development')
      return
    }

    async function registerServiceWorker() {
      try {
        // Check if service worker is already registered (without path parameter)
        const existingRegistration = await navigator.serviceWorker.getRegistration('/')
        
        if (existingRegistration) {
          console.log('✅ Service Worker already registered:', existingRegistration.scope)
          return existingRegistration
        }
        
        // Register new service worker
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/',
          updateViaCache: 'none'
        })
        
        console.log('✅ Service Worker registered successfully:', registration.scope)
        
        // Check for updates
        registration.addEventListener('updatefound', () => {
          console.log('🔄 Service Worker update found')
        })
        
        return registration
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }

    // Only register when document is ready
    if (document.readyState === 'complete') {
      registerServiceWorker()
    } else {
      const handleLoad = () => registerServiceWorker()
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [])

  return null // This component doesn't render anything
}
