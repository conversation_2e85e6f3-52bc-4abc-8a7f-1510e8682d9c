'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { GroupCreateModal } from './GroupCreateModal'
import { GroupInviteModal } from './GroupInviteModal'
import { GroupTokenStatus } from './GroupTokenStatus'
import { GroupHistoryModal } from './GroupHistoryModal'
import { GroupManagementModal } from './GroupManagementModal'

interface Group {
  id: string
  name: string
  inviteCode: string
  isCreator: boolean
  memberCount: number
  createdAt: string
  joinedAt: string
}

interface GroupSelectorProps {
  selectedGroupId: string | null
  onGroupSelect: (groupId: string | null) => void
  onGroupCreated?: (group: any) => void
  currentGroupName?: string
}

export function GroupSelector({ selectedGroupId, onGroupSelect, onGroupCreated, currentGroupName }: GroupSelectorProps) {
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showInviteModal, setShowInviteModal] = useState<Group | null>(null)
  const [showTokenStatus, setShowTokenStatus] = useState<Group | null>(null)
  const [showHistoryModal, setShowHistoryModal] = useState<Group | null>(null)
  const [showManagementModal, setShowManagementModal] = useState<Group | null>(null)
  const [user, setUser] = useState<any>(null)

  const supabase = createSupabaseClient()

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  useEffect(() => {
    loadGroups()
  }, [])

  const loadGroups = async () => {
    try {
      const response = await fetch('/api/genyus/group/list', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        setGroups(data.groups || [])
      }
    } catch (error) {
      console.error('Error loading groups:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGroupCreated = (newGroup: any) => {
    setGroups(prev => [...prev, newGroup])
    setShowCreateModal(false)
    if (onGroupCreated) {
      onGroupCreated(newGroup)
    }
  }

  const handleInviteClick = (e: React.MouseEvent, group: Group) => {
    e.stopPropagation()
    setShowInviteModal(group)
  }

  const handleDeleteGroup = async (e: React.MouseEvent, group: Group) => {
    e.stopPropagation()

    if (!confirm(`Are you sure you want to delete "${group.name}"? This cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/genyus/group/${group.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setGroups(prev => prev.filter(g => g.id !== group.id))
        if (selectedGroupId === group.id) {
          onGroupSelect(null)
        }
      } else {
        alert('Failed to delete group')
      }
    } catch (error) {
      console.error('Error deleting group:', error)
      alert('Failed to delete group')
    }
  }

  if (loading) {
    return (
      <div className="p-4 border-b border-gray-200">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Minimal Group Indicator - Only show when in a group */}
      {selectedGroupId && (
        <div className="border-b border-gray-100 bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {currentGroupName || groups.find(g => g.id === selectedGroupId)?.name || 'Group Chat'}
                  </div>
                  <div className="text-xs text-gray-500">
                    {groups.find(g => g.id === selectedGroupId)?.memberCount} members
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-1">
                <button
                  onClick={(e) => {
                    const group = groups.find(g => g.id === selectedGroupId)
                    if (group) handleInviteClick(e, group)
                  }}
                  className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                  title="Invite members"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    const group = groups.find(g => g.id === selectedGroupId)
                    if (group) setShowManagementModal(group)
                  }}
                  className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Manage group"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
                <button
                  onClick={() => onGroupSelect(null)}
                  className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                  title="Exit group chat"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Modals */}
      <GroupCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onGroupCreated={handleGroupCreated}
      />

      {showInviteModal && (
        <GroupInviteModal
          isOpen={true}
          onClose={() => setShowInviteModal(null)}
          groupId={showInviteModal.id}
          groupName={showInviteModal.name}
          inviteCode={showInviteModal.inviteCode}
        />
      )}

      {showTokenStatus && (
        <GroupTokenStatus
          isOpen={true}
          onClose={() => setShowTokenStatus(null)}
          groupId={showTokenStatus.id}
          groupName={showTokenStatus.name}
        />
      )}

      {showHistoryModal && (
        <GroupHistoryModal
          isOpen={true}
          onClose={() => setShowHistoryModal(null)}
          groupId={showHistoryModal.id}
          groupName={showHistoryModal.name}
        />
      )}

      {showManagementModal && (
        <GroupManagementModal
          isOpen={true}
          onClose={() => setShowManagementModal(null)}
          groupId={showManagementModal.id}
          groupName={showManagementModal.name}
          isCreator={showManagementModal.isCreator}
          onGroupDeleted={() => {
            setGroups(prev => prev.filter(g => g.id !== showManagementModal.id))
            onGroupSelect(null)
            setShowManagementModal(null)

            // Clear URL parameter
            const url = new URL(window.location.href)
            url.searchParams.delete('group')
            window.history.replaceState({}, '', url.toString())
          }}
          onGroupLeft={() => {
            setGroups(prev => prev.filter(g => g.id !== showManagementModal.id))
            onGroupSelect(null)
            setShowManagementModal(null)

            // Clear URL parameter
            const url = new URL(window.location.href)
            url.searchParams.delete('group')
            window.history.replaceState({}, '', url.toString())
          }}
        />
      )}
    </>
  )
}