'use client'

import { useState, useEffect } from 'react'

interface ConversationIntelligenceIndicatorProps {
  isVisible: boolean
  reason?: string
  estimatedWaitTime?: number
  onComplete?: () => void
}

export function ConversationIntelligenceIndicator({ 
  isVisible, 
  reason, 
  estimatedWaitTime = 30,
  onComplete 
}: ConversationIntelligenceIndicatorProps) {
  const [timeRemaining, setTimeRemaining] = useState(estimatedWaitTime)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (!isVisible) return

    setIsAnimating(true)
    setTimeRemaining(estimatedWaitTime)

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(interval)
          setIsAnimating(false)
          onComplete?.()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isVisible, estimatedWaitTime, onComplete])

  if (!isVisible) return null

  const getReasonDisplay = (reason?: string) => {
    switch (reason) {
      case 'AI responded recently, waiting for user input':
        return 'Giving you space to respond...'
      case 'Multiple users active, letting them discuss':
        return 'Letting the conversation flow...'
      case 'Normal conversation flow, brief pause':
        return 'Thinking thoughtfully...'
      case 'Questions pending, responding immediately':
        return 'Preparing response...'
      default:
        return 'OnlyGenyus is being thoughtful...'
    }
  }

  const getIcon = (reason?: string) => {
    if (reason?.includes('waiting for user input')) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
    
    if (reason?.includes('letting them discuss')) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
        </svg>
      )
    }

    // Default thinking icon
    return (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    )
  }

  return (
    <div className={`
      fixed bottom-20 left-1/2 transform -translate-x-1/2 
      bg-white border border-gray-200 rounded-full px-4 py-2 shadow-lg
      transition-all duration-300 ease-in-out z-50
      ${isAnimating ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}
    `}>
      <div className="flex items-center space-x-3">
        {/* Animated icon */}
        <div className={`
          text-purple-600 transition-transform duration-1000
          ${isAnimating ? 'animate-pulse' : ''}
        `}>
          {getIcon(reason)}
        </div>

        {/* Message */}
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-800">
            {getReasonDisplay(reason)}
          </span>
          {timeRemaining > 0 && (
            <span className="text-xs text-gray-500">
              ~{timeRemaining}s
            </span>
          )}
        </div>

        {/* Progress indicator */}
        <div className="w-8 h-8 relative">
          <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
            <circle
              cx="16"
              cy="16"
              r="14"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-gray-200"
            />
            <circle
              cx="16"
              cy="16"
              r="14"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 14}`}
              strokeDashoffset={`${2 * Math.PI * 14 * (timeRemaining / estimatedWaitTime)}`}
              className="text-purple-600 transition-all duration-1000 ease-linear"
            />
          </svg>
        </div>
      </div>
    </div>
  )
}

// Hook for managing conversation intelligence state
export function useConversationIntelligence() {
  const [isWaiting, setIsWaiting] = useState(false)
  const [waitReason, setWaitReason] = useState<string>()
  const [estimatedTime, setEstimatedTime] = useState(30)

  const showWaiting = (reason: string, timeSeconds: number = 30) => {
    setWaitReason(reason)
    setEstimatedTime(timeSeconds)
    setIsWaiting(true)
  }

  const hideWaiting = () => {
    setIsWaiting(false)
    setWaitReason(undefined)
  }

  return {
    isWaiting,
    waitReason,
    estimatedTime,
    showWaiting,
    hideWaiting
  }
}

// Smart conversation status component
export function ConversationStatus({ 
  activeUsers, 
  lastActivity, 
  aiThinking = false 
}: {
  activeUsers: number
  lastActivity: Date
  aiThinking?: boolean
}) {
  const timeSinceActivity = Date.now() - lastActivity.getTime()
  const minutesAgo = Math.floor(timeSinceActivity / 60000)

  const getStatusMessage = () => {
    if (aiThinking) return 'OnlyGenyus is thinking...'
    if (activeUsers > 1) return `${activeUsers} people active`
    if (minutesAgo < 1) return 'Active now'
    if (minutesAgo < 60) return `Active ${minutesAgo}m ago`
    return 'Conversation paused'
  }

  const getStatusColor = () => {
    if (aiThinking) return 'text-purple-600'
    if (activeUsers > 1) return 'text-green-600'
    if (minutesAgo < 5) return 'text-blue-600'
    return 'text-gray-500'
  }

  return (
    <div className={`text-xs ${getStatusColor()} flex items-center space-x-1`}>
      <div className={`w-2 h-2 rounded-full ${
        aiThinking ? 'bg-purple-600 animate-pulse' :
        activeUsers > 1 ? 'bg-green-600' :
        minutesAgo < 5 ? 'bg-blue-600' : 'bg-gray-400'
      }`} />
      <span>{getStatusMessage()}</span>
    </div>
  )
}

// Conversation insights panel
export function ConversationInsights({ 
  groupId, 
  insights 
}: {
  groupId: string
  insights: {
    totalMessages: number
    activeParticipants: number
    conversationPhase: string
    lastAIResponse: Date | null
    pendingQuestions: number
  }
}) {
  return (
    <div className="bg-gray-50 rounded-lg p-3 text-sm space-y-2">
      <div className="font-medium text-gray-800">Conversation Insights</div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span className="text-gray-600">Messages:</span>
          <span className="ml-1 font-medium">{insights.totalMessages}</span>
        </div>
        
        <div>
          <span className="text-gray-600">Active:</span>
          <span className="ml-1 font-medium">{insights.activeParticipants}</span>
        </div>
        
        <div>
          <span className="text-gray-600">Phase:</span>
          <span className="ml-1 font-medium capitalize">{insights.conversationPhase}</span>
        </div>
        
        <div>
          <span className="text-gray-600">Questions:</span>
          <span className="ml-1 font-medium">{insights.pendingQuestions}</span>
        </div>
      </div>
      
      {insights.lastAIResponse && (
        <div className="text-xs text-gray-600">
          Last AI response: {new Date(insights.lastAIResponse).toLocaleTimeString()}
        </div>
      )}
    </div>
  )
}
