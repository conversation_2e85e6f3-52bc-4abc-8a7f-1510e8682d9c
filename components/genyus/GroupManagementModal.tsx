'use client'

import { useState } from 'react'

interface GroupManagementModalProps {
  isOpen: boolean
  onClose: () => void
  groupId: string
  groupName: string
  isCreator: boolean
  onGroupDeleted?: () => void
  onGroupLeft?: () => void
}

export function GroupManagementModal({
  isOpen,
  onClose,
  groupId,
  groupName,
  isCreator,
  onGroupDeleted,
  onGroupLeft
}: GroupManagementModalProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)
  const [error, setError] = useState('')

  const handleDeleteGroup = async () => {
    if (!isCreator) return
    
    setIsDeleting(true)
    setError('')

    try {
      const response = await fetch(`/api/genyus/group/${groupId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        onGroupDeleted?.()
        onClose()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to delete group')
      }
    } catch (error) {
      setError('Failed to delete group')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleLeaveGroup = async () => {
    setIsLeaving(true)
    setError('')

    try {
      // For now, we'll deactivate membership (no dedicated leave endpoint yet)
      const response = await fetch(`/api/genyus/group/${groupId}/leave`, {
        method: 'POST'
      })

      if (response.ok) {
        onGroupLeft?.()
        onClose()
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to leave group')
      }
    } catch (error) {
      setError('Failed to leave group')
    } finally {
      setIsLeaving(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-md w-full shadow-2xl">
        
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Manage Group</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">{groupName}</h3>
            <p className="text-sm text-gray-600">
              {isCreator ? 'You created this group' : 'You are a member of this group'}
            </p>
          </div>

          {error && (
            <div className="p-3 bg-red-100 text-red-800 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="space-y-3">
            {isCreator ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-900 mb-2">Delete Group</h4>
                <p className="text-sm text-red-700 mb-3">
                  This will permanently delete the group and all its messages. This action cannot be undone.
                </p>
                <button
                  onClick={handleDeleteGroup}
                  disabled={isDeleting}
                  className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isDeleting ? 'Deleting...' : 'Delete Group Permanently'}
                </button>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-900 mb-2">Leave Group</h4>
                <p className="text-sm text-yellow-700 mb-3">
                  You will no longer receive messages from this group. You can be re-invited later.
                </p>
                <button
                  onClick={handleLeaveGroup}
                  disabled={isLeaving}
                  className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLeaving ? 'Leaving...' : 'Leave Group'}
                </button>
              </div>
            )}
          </div>

          <div className="pt-4 border-t">
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
