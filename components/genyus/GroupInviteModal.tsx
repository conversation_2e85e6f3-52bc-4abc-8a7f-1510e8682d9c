'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface GroupInviteModalProps {
  isOpen: boolean
  onClose: () => void
  groupId: string
  groupName: string
  inviteCode: string
  onInviteSuccess?: () => void
}

interface User {
  id: string
  name: string
  email: string
  profile_picture_url?: string
}

export function GroupInviteModal({ isOpen, onClose, groupId, groupName, inviteCode, onInviteSuccess }: GroupInviteModalProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [inviting, setInviting] = useState<string | null>(null)
  const [message, setMessage] = useState('')
  
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (searchQuery.length >= 2) {
      searchUsers()
    } else {
      setSearchResults([])
    }
  }, [searchQuery])

  const searchUsers = async () => {
    setLoading(true)
    try {
      const { data: users } = await supabase
        .from('users')
        .select('id, name, email, profile_picture_url')
        .or(`name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`)
        .limit(10)

      setSearchResults(users || [])
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }

  const inviteUser = async (userId: string) => {
    setInviting(userId)
    setMessage('')
    
    try {
      const response = await fetch('/api/genyus/group/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          groupId,
          inviteeUserId: userId
        })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage('Invitation sent successfully!')
        setSearchQuery('')
        setSearchResults([])

        // Call success callback after a brief delay to show the success message
        setTimeout(() => {
          onInviteSuccess?.()
          onClose()
        }, 1500)
      } else {
        setMessage(data.error || 'Failed to send invitation')
      }
    } catch (error) {
      setMessage('Failed to send invitation')
    } finally {
      setInviting(null)
    }
  }

  const copyInviteLink = async () => {
    const inviteLink = `${window.location.origin}/genyus/group/join/${inviteCode}`
    try {
      await navigator.clipboard.writeText(inviteLink)
      setMessage('Invite link copied to clipboard!')
    } catch (error) {
      setMessage('Failed to copy link')
    }
  }

  const shareInviteLink = async () => {
    const inviteLink = `${window.location.origin}/genyus/group/join/${inviteCode}`
    const shareText = `Join me in "${groupName}" group chat on OnlyGenyus! ${inviteLink}`

    if (navigator.share) {
      try {
        await navigator.share({
          title: `Join ${groupName} on OnlyGenyus`,
          text: shareText,
          url: inviteLink
        })
      } catch (error) {
        // User cancelled or error occurred
        await copyInviteLink()
      }
    } else {
      await copyInviteLink()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Invite to {groupName}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Share Link Section */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">Share Invite Link</h3>
            <div className="flex space-x-2">
              <button
                onClick={copyInviteLink}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Copy Link
              </button>
              <button
                onClick={shareInviteLink}
                className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Share
              </button>
            </div>
          </div>

          {/* Search Users Section */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">Invite OnlyDiary Users</h3>
            <div className="relative">
              <input
                type="text"
                placeholder="Search by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {loading && (
                <div className="absolute right-3 top-2.5">
                  <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
              )}
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
                {searchResults.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        {user.profile_picture_url ? (
                          <img
                            src={user.profile_picture_url}
                            alt={user.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-medium text-gray-600">
                            {(user.name || user.email).charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{user.name || user.email}</div>
                        {user.name && (
                          <div className="text-xs text-gray-500">{user.email}</div>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => inviteUser(user.id)}
                      disabled={inviting === user.id}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {inviting === user.id ? 'Inviting...' : 'Invite'}
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message */}
          {message && (
            <div className={`p-3 rounded-lg text-sm ${
              message.includes('success') || message.includes('copied')
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {message}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
