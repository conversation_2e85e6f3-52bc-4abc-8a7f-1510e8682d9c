'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { FloatingGroupReturn } from './FloatingGroupReturn'

export function FloatingGroupReturnWrapper() {
  const [userId, setUserId] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUserId(user?.id || null)
    }

    getUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUserId(session?.user?.id || null)
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  if (!userId) return null

  return <FloatingGroupReturn userId={userId} />
}
