'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface GroupHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  groupId: string
  groupName: string
}

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  user: {
    id: string
    name: string
    profilePictureUrl?: string
  }
}

export function GroupHistoryModal({ isOpen, onClose, groupId, groupName }: GroupHistoryModalProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [hasMore, setHasMore] = useState(false)
  const [offset, setOffset] = useState(0)
  
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen && groupId) {
      loadHistory()
    }
  }, [isOpen, groupId])

  const loadHistory = async (search = '', reset = true) => {
    setLoading(true)
    try {
      const currentOffset = reset ? 0 : offset
      const params = new URLSearchParams({
        limit: '50',
        offset: currentOffset.toString(),
        ...(search && { search })
      })

      const response = await fetch(`/api/genyus/group/${groupId}/history?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        
        if (reset) {
          setMessages(data.messages)
        } else {
          setMessages(prev => [...prev, ...data.messages])
        }
        
        setHasMore(data.pagination.hasMore)
        setOffset(currentOffset + data.messages.length)
      }
    } catch (error) {
      console.error('Failed to load history:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setOffset(0)
    loadHistory(query, true)
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      loadHistory(searchQuery, false)
    }
  }

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Conversation History</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="text-sm text-gray-600 mb-4">{groupName}</div>
          
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <svg className="w-4 h-4 text-gray-400 absolute left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading && messages.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Loading conversation history...</p>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? 'No messages found matching your search.' : 'No conversation history yet.'}
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((message, index) => {
                const showDate = index === 0 || 
                  formatDate(message.timestamp) !== formatDate(messages[index - 1].timestamp)
                
                return (
                  <div key={message.id}>
                    {showDate && (
                      <div className="text-center text-xs text-gray-400 mb-4">
                        {formatDate(message.timestamp)}
                      </div>
                    )}
                    
                    <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      {message.type === 'user' ? (
                        <div className="max-w-[70%]">
                          <div className="flex items-center justify-end space-x-2 mb-1">
                            <span className="text-xs text-gray-500">{message.user.name}</span>
                            <div className="w-5 h-5 rounded-full overflow-hidden bg-gray-200">
                              {message.user.profilePictureUrl ? (
                                <img
                                  src={message.user.profilePictureUrl}
                                  alt={message.user.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-blue-100">
                                  <span className="text-xs font-medium text-blue-600">
                                    {message.user.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="bg-blue-600 text-white rounded-2xl px-4 py-2">
                            <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                            <div className="text-xs text-blue-100 mt-1 opacity-75">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="max-w-[70%]">
                          <div className="flex items-center space-x-2 mb-1">
                            <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            </div>
                            <span className="text-xs text-gray-500">OnlyGenyus</span>
                          </div>
                          <div className="bg-gray-50 rounded-2xl px-4 py-2 border border-gray-100">
                            <div className="text-sm text-gray-900 whitespace-pre-wrap">{message.content}</div>
                            <div className="text-xs text-gray-400 mt-1">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
              
              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-4">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
