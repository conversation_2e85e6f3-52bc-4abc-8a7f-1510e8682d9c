'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface GroupTokenStatusProps {
  groupId: string
  isVisible: boolean
  onClose: () => void
}

interface MemberTokenStatus {
  userId: string
  name: string
  wordsRemaining: number
  isEligible: boolean
}

export function GroupTokenStatus({ groupId, isVisible, onClose }: GroupTokenStatusProps) {
  const [members, setMembers] = useState<MemberTokenStatus[]>([])
  const [loading, setLoading] = useState(false)
  
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isVisible && groupId) {
      checkGroupTokenStatus()
    }
  }, [isVisible, groupId])

  const checkGroupTokenStatus = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .rpc('check_group_token_eligibility', { group_id_param: groupId })

      if (error) {
        console.error('Token status check failed:', error)
        return
      }

      // Get member names
      const { data: memberData } = await supabase
        .from('genyus_group_members')
        .select(`
          user_id,
          users!inner(name, email)
        `)
        .eq('group_id', groupId)
        .eq('is_active', true)

      const memberMap = new Map(
        (memberData || []).map(m => [m.user_id, m.users.name || m.users.email])
      )

      const memberStatus: MemberTokenStatus[] = (data || []).map((member: any) => ({
        userId: member.user_id,
        name: memberMap.get(member.user_id) || 'Unknown',
        wordsRemaining: member.words_remaining,
        isEligible: member.is_eligible
      }))

      setMembers(memberStatus)
    } catch (error) {
      console.error('Failed to check token status:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isVisible) return null

  const ineligibleCount = members.filter(m => !m.isEligible).length
  const allEligible = ineligibleCount === 0

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Group Token Status</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Checking token status...</p>
            </div>
          ) : (
            <>
              {/* Status Summary */}
              <div className={`p-4 rounded-lg mb-6 ${
                allEligible ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center space-x-2">
                  <div className={`w-5 h-5 rounded-full ${
                    allEligible ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className={`font-medium ${
                    allEligible ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {allEligible 
                      ? 'All members eligible for group chat'
                      : `${ineligibleCount} member${ineligibleCount !== 1 ? 's' : ''} need${ineligibleCount === 1 ? 's' : ''} to upgrade`
                    }
                  </span>
                </div>
              </div>

              {/* Member List */}
              <div className="space-y-3">
                <h3 className="font-medium text-gray-900">Member Token Status</h3>
                <div className="space-y-2">
                  {members.map((member) => (
                    <div key={member.userId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          member.isEligible ? 'bg-green-500' : 'bg-red-500'
                        }`}></div>
                        <span className="font-medium text-gray-900">{member.name}</span>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${
                          member.isEligible ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {member.wordsRemaining === -1 
                            ? 'Unlimited' 
                            : `${member.wordsRemaining.toLocaleString()} words`
                          }
                        </div>
                        {!member.isEligible && (
                          <div className="text-xs text-red-500">Needs upgrade</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-6 space-y-3">
                {!allEligible && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      <strong>Note:</strong> Group chat requires all members to have sufficient tokens. 
                      Members with insufficient tokens will need to upgrade before the group can continue chatting.
                    </p>
                  </div>
                )}
                
                <button
                  onClick={checkGroupTokenStatus}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Refresh Status
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
