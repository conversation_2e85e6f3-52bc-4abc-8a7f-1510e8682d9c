'use client'

import { useState, useRef, useEffect, forwardRef } from 'react'

interface User {
  id: string
  name: string
  profilePictureUrl?: string
}

interface MentionInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: () => void
  placeholder?: string
  disabled?: boolean
  groupMembers?: User[]
  className?: string
}

export const MentionInput = forwardRef<HTMLTextAreaElement, MentionInputProps>(({
  value,
  onChange,
  onSubmit,
  placeholder = "Type your message...",
  disabled = false,
  groupMembers = [],
  className = ""
}, ref) => {
  const [showMentions, setShowMentions] = useState(false)
  const [mentionQuery, setMentionQuery] = useState('')
  const [mentionPosition, setMentionPosition] = useState(0)
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Use forwarded ref or fallback to local ref
  const inputRef = (ref as React.RefObject<HTMLTextAreaElement>) || textareaRef

  // Filter members based on mention query
  const filteredMembers = groupMembers.filter(member =>
    member.name.toLowerCase().includes(mentionQuery.toLowerCase())
  )

  // Add AI as a mentionable option
  const mentionOptions = [
    { id: 'ai', name: 'OnlyGenyus', profilePictureUrl: undefined },
    ...filteredMembers
  ].filter(option => 
    option.name.toLowerCase().includes(mentionQuery.toLowerCase())
  )

  useEffect(() => {
    // Auto-resize textarea
    if (inputRef.current) {
      inputRef.current.style.height = 'auto'
      inputRef.current.style.height = inputRef.current.scrollHeight + 'px'
    }
  }, [value])

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    const cursorPosition = e.target.selectionStart
    
    // Check for @ mention trigger
    const textBeforeCursor = newValue.slice(0, cursorPosition)
    const lastAtIndex = textBeforeCursor.lastIndexOf('@')
    
    if (lastAtIndex !== -1) {
      const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1)
      
      // Check if we're in a mention context (no spaces after @)
      if (!textAfterAt.includes(' ') && textAfterAt.length <= 20) {
        setMentionQuery(textAfterAt)
        setMentionPosition(lastAtIndex)
        setShowMentions(true)
        setSelectedMentionIndex(0)
      } else {
        setShowMentions(false)
      }
    } else {
      setShowMentions(false)
    }
    
    onChange(newValue)
  }

  const insertMention = (user: User) => {
    const beforeMention = value.slice(0, mentionPosition)
    const afterMention = value.slice(mentionPosition + mentionQuery.length + 1)
    const newValue = `${beforeMention}@${user.name} ${afterMention}`
    
    onChange(newValue)
    setShowMentions(false)
    
    // Focus back to textarea
    setTimeout(() => {
      if (inputRef.current) {
        const newCursorPosition = mentionPosition + user.name.length + 2
        inputRef.current.focus()
        inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition)
      }
    }, 0)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showMentions && mentionOptions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedMentionIndex(prev => 
          prev < mentionOptions.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedMentionIndex(prev => 
          prev > 0 ? prev - 1 : mentionOptions.length - 1
        )
      } else if (e.key === 'Enter' || e.key === 'Tab') {
        e.preventDefault()
        insertMention(mentionOptions[selectedMentionIndex])
      } else if (e.key === 'Escape') {
        setShowMentions(false)
      }
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      onSubmit()
    }
  }

  return (
    <div className="relative">
      <textarea
        ref={inputRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className={`resize-none overflow-hidden min-h-[44px] max-h-32 ${className}`}
        rows={1}
      />
      
      {/* Mention dropdown */}
      {showMentions && mentionOptions.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto z-50">
          {mentionOptions.map((option, index) => (
            <button
              key={option.id}
              onClick={() => insertMention(option)}
              className={`w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 ${
                index === selectedMentionIndex ? 'bg-blue-50 border-l-2 border-blue-500' : ''
              }`}
            >
              <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
                {option.profilePictureUrl ? (
                  <img
                    src={option.profilePictureUrl}
                    alt={option.name}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-gray-600">
                    {option.id === 'ai' ? '🤖' : option.name.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
              <span className="text-sm font-medium text-gray-900">
                {option.name}
                {option.id === 'ai' && <span className="text-xs text-purple-600 ml-1">(AI)</span>}
              </span>
            </button>
          ))}
        </div>
      )}
      
      {/* Mention hint */}
      {groupMembers.length > 0 && !showMentions && (
        <div className="absolute top-2 right-2 text-xs text-gray-400 pointer-events-none">
          @ mention ({groupMembers.length} people)
        </div>
      )}
    </div>
  )
})

MentionInput.displayName = 'MentionInput'

// Utility function to parse mentions from text
export function parseMentions(text: string, groupMembers: User[]): {
  text: string
  mentions: User[]
} {
  const mentionPattern = /@(\w+)/g
  const mentions: User[] = []
  
  const processedText = text.replace(mentionPattern, (match, username) => {
    const user = groupMembers.find(member => 
      member.name.toLowerCase() === username.toLowerCase()
    )
    
    if (user && !mentions.find(m => m.id === user.id)) {
      mentions.push(user)
    }
    
    return match // Keep the original @username in text
  })
  
  return { text: processedText, mentions }
}

// Component to render text with highlighted mentions
export function TextWithMentions({ 
  text, 
  groupMembers, 
  className = "" 
}: {
  text: string
  groupMembers: User[]
  className?: string
}) {
  const parts = text.split(/(@\w+)/g)
  
  return (
    <span className={className}>
      {parts.map((part, index) => {
        if (part.startsWith('@')) {
          const username = part.slice(1)
          const user = groupMembers.find(member => 
            member.name.toLowerCase() === username.toLowerCase()
          )
          
          if (user) {
            return (
              <span
                key={index}
                className="bg-blue-100 text-blue-800 px-1 rounded font-medium"
              >
                {part}
              </span>
            )
          }
        }
        
        return <span key={index}>{part}</span>
      })}
    </span>
  )
}
