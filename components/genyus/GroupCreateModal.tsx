'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface GroupCreateModalProps {
  isOpen: boolean
  onClose: () => void
  onGroupCreated: (group: any) => void
  onInviteSent?: (group: any) => void
}

export function GroupCreateModal({ isOpen, onClose, onGroupCreated, onInviteSent }: GroupCreateModalProps) {
  const [groupName, setGroupName] = useState('')
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')
  const [step, setStep] = useState<'create' | 'invite'>('create')
  const [createdGroup, setCreatedGroup] = useState<any>(null)

  // User search and invite state
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Array<{id: string, name: string, profile_picture_url?: string}>>([])
  const [selectedUser, setSelectedUser] = useState<{id: string, name: string} | null>(null)
  const [quickPicks, setQuickPicks] = useState<Array<{id: string, name: string, profile_picture_url?: string}>>([])
  const [searching, setSearching] = useState(false)
  const [inviting, setInviting] = useState(false)
  const [inviteError, setInviteError] = useState('')
  const [inviteSuccess, setInviteSuccess] = useState('')

  const supabase = createSupabaseClient()

  // Load quick picks: people you follow
  useEffect(() => {
    let cancelled = false
    async function loadQuickPicks() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) return

        const { data } = await supabase
          .from('follows')
          .select(`
            writer_id,
            users:users!follows_writer_id_fkey(id, name, profile_picture_url, avatar)
          `)
          .eq('follower_id', user.id)
          .limit(50) // Show more people

        if (!cancelled) {
          const picks = (data || []).map((r: any) => r.users).filter(Boolean)
          setQuickPicks(picks) // Show all people you follow
        }
      } catch (error) {
        console.error('Error loading quick picks:', error)
      }
    }
    loadQuickPicks()
    return () => { cancelled = true }
  }, [supabase])

  // Search users
  useEffect(() => {
    let cancelled = false
    async function searchUsers() {
      if (!searchQuery || searchQuery.length < 2) {
        setSearchResults([])
        return
      }

      setSearching(true)
      try {
        const { data } = await supabase
          .from('users')
          .select('id, name, profile_picture_url, avatar')
          .ilike('name', `%${searchQuery}%`)
          .limit(8)

        if (!cancelled) {
          setSearchResults(data || [])
        }
      } catch (error) {
        console.error('Search error:', error)
      } finally {
        if (!cancelled) setSearching(false)
      }
    }

    const timer = setTimeout(searchUsers, 300)
    return () => {
      cancelled = true
      clearTimeout(timer)
    }
  }, [searchQuery, supabase])

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!groupName.trim()) return

    setCreating(true)
    setError('')

    try {
      const response = await fetch('/api/genyus/group/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: groupName.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        setCreatedGroup(data.group)
        setStep('invite') // Move to invite step
      } else {
        setError(data.error || 'Failed to create group')
      }
    } catch (error) {
      setError('Failed to create group')
    } finally {
      setCreating(false)
    }
  }

  const handleInvite = async () => {
    if (!selectedUser || !createdGroup) return

    setInviting(true)
    setInviteError('')
    setInviteSuccess('')

    try {
      const response = await fetch('/api/genyus/group/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          groupId: createdGroup.id,
          inviteeUserId: selectedUser.id
        })
      })

      const data = await response.json()

      if (response.ok) {
        setInviteSuccess(`Invitation sent to ${selectedUser.name}!`)

        // Auto-redirect to group chat after successful invite
        setTimeout(() => {
          onInviteSent?.(createdGroup)
          handleClose()
        }, 1500) // Give time to see success message

        setSelectedUser(null)
        setSearchQuery('')
        setSearchResults([])
      } else {
        setInviteError(data.error || 'Failed to send invitation')
      }
    } catch (error) {
      setInviteError('Failed to send invitation')
    } finally {
      setInviting(false)
    }
  }

  const handleFinish = () => {
    onGroupCreated(createdGroup)
    handleClose()
  }

  const handleSkipInvite = () => {
    onGroupCreated(createdGroup)
    handleClose()
  }

  const handleClose = () => {
    setGroupName('')
    setError('')
    setStep('create')
    setCreatedGroup(null)
    setSearchQuery('')
    setSearchResults([])
    setSelectedUser(null)
    setInviteError('')
    setInviteSuccess('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">
                {step === 'create' ? 'Create Group Chat' : 'Invite People'}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {step === 'create' ? 'Step 1 of 2' : 'Step 2 of 2'}
              </p>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          {step === 'create' ? (
            // Step 1: Create Group
            <form onSubmit={handleCreateGroup} className="space-y-4">
              <div>
                <label htmlFor="groupName" className="block text-sm font-medium text-gray-700 mb-2">
                  Group Name
                </label>
                <input
                  id="groupName"
                  type="text"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="e.g., Study Group, Work Team, Friends..."
                  maxLength={50}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  autoFocus
                />
                <div className="text-xs text-gray-500 mt-1">
                  {groupName.length}/50 characters
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-100 text-red-800 rounded-lg text-sm">
                  {error}
                </div>
              )}

              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">What happens next:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• You'll be able to invite OnlyDiary users instantly</li>
                  <li>• AI joins your conversation like a brilliant team member</li>
                  <li>• Share invite links for people to join the platform</li>
                </ul>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleClose}
                  className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!groupName.trim() || creating}
                  className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {creating ? 'Creating...' : 'Create Group'}
                </button>
              </div>
            </form>
          ) : (
            // Step 2: Invite People
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium text-green-900">Group "{createdGroup?.name}" created!</span>
                </div>
              </div>

              <div className="space-y-4">
                {/* Quick picks: people you follow */}
                {(!searchQuery || searchQuery.length === 0) && quickPicks.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-3">
                      People you follow ({quickPicks.length})
                    </div>
                    <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-2">
                      <div className="grid grid-cols-2 gap-2">
                        {quickPicks.map((user) => (
                          <button
                            key={user.id}
                            onClick={() => setSelectedUser({ id: user.id, name: user.name })}
                            className={`w-full flex items-center gap-2 border rounded-lg px-3 py-2 bg-white hover:border-gray-300 transition ${
                              selectedUser?.id === user.id ? 'border-blue-300 ring-2 ring-blue-400 bg-blue-50' : 'border-gray-200'
                            }`}
                          >
                            <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-200">
                              {user.profile_picture_url ? (
                                <img src={user.profile_picture_url} alt={user.name} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center text-xs text-gray-600">
                                  {user.name.charAt(0).toUpperCase()}
                                </div>
                              )}
                            </div>
                            <span className="text-sm font-medium truncate">{user.name}</span>
                            {selectedUser?.id === user.id && (
                              <svg className="w-4 h-4 text-blue-600 ml-auto flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search OnlyDiary users
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search by name..."
                      className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔎</span>
                    {searching && (
                      <div className="absolute right-3 top-1/2 -translate-y-1/2">
                        <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
                    {searchResults.map((user) => (
                      <button
                        key={user.id}
                        onClick={() => setSelectedUser({ id: user.id, name: user.name })}
                        className={`w-full flex items-center gap-3 p-3 text-left hover:bg-gray-50 transition ${
                          selectedUser?.id === user.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                        }`}
                      >
                        <div className="w-9 h-9 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center">
                          {user.profile_picture_url ? (
                            <img src={user.profile_picture_url} alt={user.name} className="w-full h-full object-cover" />
                          ) : (
                            <span className="text-sm font-medium text-gray-600">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        <span className="font-medium">{user.name}</span>
                        {selectedUser?.id === user.id && (
                          <svg className="w-5 h-5 text-blue-600 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                      </button>
                    ))}
                  </div>
                )}

                {/* Selected User */}
                {selectedUser && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-800">
                            {selectedUser.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <span className="font-medium text-blue-900">Ready to invite {selectedUser.name}</span>
                      </div>
                      <button
                        onClick={() => setSelectedUser(null)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}

                {inviteError && (
                  <div className="p-3 bg-red-100 text-red-800 rounded-lg text-sm">
                    {inviteError}
                  </div>
                )}

                {inviteSuccess && (
                  <div className="p-3 bg-green-100 text-green-800 rounded-lg text-sm">
                    {inviteSuccess}
                  </div>
                )}

                {selectedUser && (
                  <button
                    onClick={handleInvite}
                    disabled={inviting}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {inviting ? 'Sending invitation...' : `Invite ${selectedUser.name}`}
                  </button>
                )}

                {/* Invite Link for Non-Platform Users */}
                <div className="border-t pt-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="w-5 h-5 text-gray-600 mt-0.5">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 mb-1">Invite anyone with a link</p>
                        <p className="text-sm text-gray-600 mb-3">Share this link for people to join OnlyDiary and your group</p>
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={`${window.location.origin}/genyus/group/join/${createdGroup?.invite_code || ''}`}
                            readOnly
                            className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white"
                          />
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(`${window.location.origin}/genyus/group/join/${createdGroup?.invite_code || ''}`)
                              // Could add a toast notification here
                            }}
                            className="px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                          >
                            Copy
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex space-x-3">
                  <button
                    onClick={handleSkipInvite}
                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Skip for now
                  </button>
                  <button
                    onClick={handleFinish}
                    className="flex-1 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Start Chatting
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
