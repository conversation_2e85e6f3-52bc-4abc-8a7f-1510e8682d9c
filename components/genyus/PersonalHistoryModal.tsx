'use client'

import { useState, useEffect } from 'react'

interface PersonalHistoryModalProps {
  isOpen: boolean
  onClose: () => void
}

interface Conversation {
  id: string
  question: {
    content: string
    timestamp: string
  }
  answer: {
    content: string
    timestamp: string
  }
}

interface ConversationSession {
  id: string
  title: string
  topic_category: string
  session_summary: string
  started_at: string
  ended_at: string | null
  message_count: number
}

export function PersonalHistoryModal({ isOpen, onClose }: PersonalHistoryModalProps) {
  const [activeTab, setActiveTab] = useState<'sessions' | 'messages'>('sessions')
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [sessions, setSessions] = useState<ConversationSession[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [hasMore, setHasMore] = useState(false)
  const [offset, setOffset] = useState(0)
  const [selectedSession, setSelectedSession] = useState<ConversationSession | null>(null)
  const [sessionMessages, setSessionMessages] = useState<Array<{role: string, content: string, timestamp: string}>>([])
  const [loadingSession, setLoadingSession] = useState(false)

  useEffect(() => {
    if (isOpen) {
      if (activeTab === 'sessions') {
        loadSessions()
      } else {
        loadHistory()
      }
    }
  }, [isOpen, activeTab])

  const loadSessions = async (reset = true) => {
    setLoading(true)
    try {
      const currentOffset = reset ? 0 : offset
      const params = new URLSearchParams({
        limit: '20',
        offset: currentOffset.toString()
      })

      const response = await fetch(`/api/genyus/sessions?${params}`)

      if (response.ok) {
        const data = await response.json()

        if (reset) {
          setSessions(data.sessions || [])
          setOffset(20)
        } else {
          setSessions(prev => [...prev, ...(data.sessions || [])])
          setOffset(prev => prev + 20)
        }

        setHasMore(data.pagination?.hasMore || false)
      }
    } catch (error) {
      console.error('Error loading conversation sessions:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSessionMessages = async (sessionId: string) => {
    setLoadingSession(true)
    try {
      const response = await fetch(`/api/genyus/sessions/${sessionId}/messages`)

      if (response.ok) {
        const data = await response.json()
        setSessionMessages(data.messages || [])
      }
    } catch (error) {
      console.error('Error loading session messages:', error)
    } finally {
      setLoadingSession(false)
    }
  }

  const loadHistory = async (search = '', reset = true) => {
    setLoading(true)
    try {
      const currentOffset = reset ? 0 : offset
      const params = new URLSearchParams({
        limit: '25',
        offset: currentOffset.toString(),
        ...(search && { search })
      })

      const response = await fetch(`/api/genyus/history?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        
        if (reset) {
          setConversations(data.conversations)
        } else {
          setConversations(prev => [...prev, ...data.conversations])
        }
        
        setHasMore(data.pagination.hasMore)
        setOffset(currentOffset + data.conversations.length)
      }
    } catch (error) {
      console.error('Failed to load history:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setOffset(0)
    loadHistory(query, true)
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      loadHistory(searchQuery, false)
    }
  }

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  // Simple markdown renderer for AI responses
  const renderMarkdown = (text: string): string => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
      .replace(/\n\n/g, '</p><p class="mb-2">')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)/, '<p class="mb-2">$1')
      .replace(/(.+)$/, '$1</p>')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Conversation History</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="text-sm text-gray-600 mb-4">Your personal conversations with OnlyGenyus</div>

          {/* Tabs */}
          <div className="flex space-x-1 mb-4">
            <button
              onClick={() => setActiveTab('sessions')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'sessions'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              📚 Conversations
            </button>
            <button
              onClick={() => setActiveTab('messages')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === 'messages'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              💬 All Messages
            </button>
          </div>

          {/* Search - only show for messages tab */}
          {activeTab === 'messages' && (
            <div className="relative">
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <svg className="w-4 h-4 text-gray-400 absolute left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'sessions' ? (
            /* Conversation Sessions */
            selectedSession ? (
              /* Session Messages View */
              <div>
                <div className="flex items-center mb-6">
                  <button
                    onClick={() => setSelectedSession(null)}
                    className="mr-3 p-1 hover:bg-gray-100 rounded"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <div>
                    <h3 className="font-semibold text-lg">{selectedSession.title}</h3>
                    <p className="text-sm text-gray-600">{selectedSession.session_summary}</p>
                  </div>
                </div>

                {loadingSession ? (
                  <div className="text-center py-8">
                    <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading conversation...</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {sessionMessages.map((message, index) => (
                      <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        {message.role === 'user' ? (
                          <div className="max-w-[80%] bg-blue-600 text-white rounded-2xl px-5 py-3">
                            <div className="font-medium leading-relaxed whitespace-pre-wrap">
                              {message.content}
                            </div>
                            <div className="text-xs text-blue-100 mt-2 opacity-75">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        ) : (
                          <div className="max-w-[90%]">
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                              </div>
                              <span className="text-sm font-medium text-gray-700">OnlyGenyus</span>
                            </div>
                            <div className="bg-gray-50 rounded-2xl px-5 py-4 border border-gray-100">
                              <div
                                className="prose prose-sm max-w-none text-gray-900 leading-relaxed"
                                dangerouslySetInnerHTML={{ __html: renderMarkdown(message.content) }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              /* Sessions List */
              loading && sessions.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading conversations...</p>
                </div>
              ) : sessions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No conversation sessions yet. Start chatting to create your first conversation!
                </div>
              ) : (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div
                      key={session.id}
                      onClick={() => {
                        setSelectedSession(session)
                        loadSessionMessages(session.id)
                      }}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{session.title}</h3>
                          <p className="text-sm text-gray-600 mb-2">{session.session_summary}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span className="px-2 py-1 bg-gray-100 rounded-full">{session.topic_category}</span>
                            <span>{session.message_count} messages</span>
                            <span>{formatDate(session.started_at)}</span>
                          </div>
                        </div>
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  ))}

                  {hasMore && (
                    <button
                      onClick={() => loadSessions(false)}
                      disabled={loading}
                      className="w-full py-3 text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
                    >
                      {loading ? 'Loading...' : 'Load More'}
                    </button>
                  )}
                </div>
              )
            )
          ) : (
            /* All Messages View */
            loading && conversations.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading conversation history...</p>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchQuery ? 'No conversations found matching your search.' : 'No conversation history yet.'}
              </div>
            ) : (
            <div className="space-y-8">
              {conversations.map((conversation, index) => {
                const showDate = index === 0 || 
                  formatDate(conversation.question.timestamp) !== formatDate(conversations[index - 1].question.timestamp)
                
                return (
                  <div key={conversation.id}>
                    {showDate && (
                      <div className="text-center text-xs text-gray-400 mb-6">
                        {formatDate(conversation.question.timestamp)}
                      </div>
                    )}
                    
                    {/* Question */}
                    <div className="flex justify-end mb-4">
                      <div className="max-w-[80%] bg-blue-600 text-white rounded-2xl px-5 py-3">
                        <div className="font-medium leading-relaxed whitespace-pre-wrap">
                          {conversation.question.content}
                        </div>
                        <div className="text-xs text-blue-100 mt-2 opacity-75">
                          {new Date(conversation.question.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>

                    {/* Answer */}
                    <div className="flex justify-start">
                      <div className="max-w-[90%]">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium text-gray-700">OnlyGenyus</span>
                        </div>
                        <div className="bg-gray-50 rounded-2xl px-5 py-4 border border-gray-100">
                          <div 
                            className="prose prose-sm max-w-none text-gray-900 leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: renderMarkdown(conversation.answer.content) }}
                          />
                          <div className="text-xs text-gray-400 mt-3">
                            {new Date(conversation.answer.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
              
              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-4">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </button>
                </div>
              )}
            </div>
            )
          )}
        </div>
      </div>
    </div>
  )
}
