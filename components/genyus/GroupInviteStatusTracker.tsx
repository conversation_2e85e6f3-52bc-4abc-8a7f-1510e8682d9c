'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface InviteStatus {
  step: 'invite_sent' | 'link_clicked' | 'user_signed_up' | 'user_signed_in' | 'user_joining' | 'user_joined'
  timestamp: Date
  message: string
}

interface GroupInviteStatusTrackerProps {
  groupId: string
  inviteCode: string
  onUserJoined?: () => void
}

export function GroupInviteStatusTracker({ 
  groupId, 
  inviteCode, 
  onUserJoined 
}: GroupInviteStatusTrackerProps) {
  const [statuses, setStatuses] = useState<InviteStatus[]>([
    {
      step: 'invite_sent',
      timestamp: new Date(),
      message: 'Invitation sent successfully'
    }
  ])
  const [isComplete, setIsComplete] = useState(false)
  
  const supabase = createSupabaseClient()

  // Real trackable steps only
  const allSteps: Array<{
    step: InviteStatus['step']
    icon: string
    label: string
    description: string
  }> = [
    {
      step: 'invite_sent',
      icon: '📤',
      label: 'Invite Sent',
      description: 'Your friend has been notified'
    },
    {
      step: 'link_clicked',
      icon: '🔗',
      label: 'Link Clicked',
      description: 'Your friend opened the invite'
    },
    {
      step: 'user_joined',
      icon: '🎉',
      label: 'Joined Successfully',
      description: 'Ready to start chatting!'
    }
  ]

  // Track invite status changes
  useEffect(() => {
    if (isComplete) return

    const trackInviteProgress = async () => {
      try {
        // Check for new group members
        const { data: members } = await supabase
          .from('genyus_group_members')
          .select('user_id, created_at, users!inner(name)')
          .eq('group_id', groupId)
          .eq('is_active', true)

        if (members && members.length > 1) {
          // Someone joined!
          const newMember = members.find(m => m.created_at > statuses[0].timestamp.toISOString())
          if (newMember) {
            setStatuses(prev => [
              ...prev,
              {
                step: 'user_joined',
                timestamp: new Date(newMember.created_at),
                message: `${newMember.users.name} joined the group!`
              }
            ])
            setIsComplete(true)
            onUserJoined?.()
            return
          }
        }

        // Check for real invite link clicks
        const { data: inviteActivity } = await supabase
          .from('genyus_group_invites')
          .select('status, clicked_at')
          .eq('group_id', groupId)
          .eq('invite_code', inviteCode)
          .eq('status', 'clicked')
          .single()

        if (inviteActivity && inviteActivity.clicked_at) {
          const hasClickedStatus = statuses.some(s => s.step === 'link_clicked')
          if (!hasClickedStatus) {
            setStatuses(prev => [
              ...prev,
              {
                step: 'link_clicked',
                timestamp: new Date(inviteActivity.clicked_at),
                message: 'Your friend opened the invite link'
              }
            ])
          }
        }

      } catch (error) {
        console.error('Error tracking invite progress:', error)
      }
    }

    // Poll every 3 seconds
    const interval = setInterval(trackInviteProgress, 3000)
    
    // Initial check
    trackInviteProgress()

    return () => clearInterval(interval)
  }, [groupId, inviteCode, statuses, isComplete, onUserJoined, supabase])

  // No simulation - only real events tracked

  const getCurrentStepIndex = () => {
    const lastStatus = statuses[statuses.length - 1]
    return allSteps.findIndex(step => step.step === lastStatus?.step) ?? 0
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-bold text-blue-900">Invite Progress</h3>
        {!isComplete && (
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm">Tracking...</span>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {allSteps.map((step, index) => {
          const isCompleted = statuses.some(s => s.step === step.step)
          const isCurrent = getCurrentStepIndex() === index
          const isPending = index > getCurrentStepIndex()
          const status = statuses.find(s => s.step === step.step)

          return (
            <div
              key={step.step}
              className={`flex items-start space-x-3 transition-all duration-500 ${
                isCompleted ? 'opacity-100' : 
                isCurrent ? 'opacity-80' : 
                'opacity-40'
              }`}
            >
              {/* Icon */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm transition-all duration-500 ${
                isCompleted ? 'bg-green-100 border-2 border-green-500' :
                isCurrent ? 'bg-blue-100 border-2 border-blue-500 animate-pulse' :
                'bg-gray-100 border-2 border-gray-300'
              }`}>
                {isCompleted ? '✅' : step.icon}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className={`font-medium ${
                    isCompleted ? 'text-green-800' :
                    isCurrent ? 'text-blue-800' :
                    'text-gray-600'
                  }`}>
                    {step.label}
                  </h4>
                  {status && (
                    <span className="text-xs text-gray-500">
                      {formatTime(status.timestamp)}
                    </span>
                  )}
                </div>
                <p className={`text-sm ${
                  isCompleted ? 'text-green-700' :
                  isCurrent ? 'text-blue-700' :
                  'text-gray-500'
                }`}>
                  {status?.message || step.description}
                </p>
              </div>

              {/* Loading indicator for current step */}
              {isCurrent && !isCompleted && (
                <div className="w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"></div>
              )}
            </div>
          )
        })}
      </div>

      {isComplete && (
        <div className="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-green-600 text-lg">🎉</span>
            <span className="font-medium text-green-800">Your friend has joined! Start chatting below.</span>
          </div>
        </div>
      )}

      {!isComplete && (
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-3">
              While you wait, feel free to explore OnlyDiary:
            </p>
            <div className="flex flex-wrap gap-2 justify-center">
              <button
                onClick={() => window.open('/timeline', '_blank')}
                className="px-3 py-1 text-xs bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                📖 Timeline
              </button>
              <button
                onClick={() => window.open('/write', '_blank')}
                className="px-3 py-1 text-xs bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                ✍️ Write
              </button>
              <button
                onClick={() => window.open('/audio', '_blank')}
                className="px-3 py-1 text-xs bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                🎵 Audio
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
