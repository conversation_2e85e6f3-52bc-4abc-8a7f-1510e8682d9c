'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface FloatingGroupReturnProps {
  userId?: string
}

interface ActiveGroup {
  id: string
  name: string
  memberCount: number
  inviteCode: string
  hasMessages: boolean
}

interface GroupOption {
  id: string
  name: string
  memberCount: number
  hasMessages: boolean
  isRecent: boolean
}

export function FloatingGroupReturn({ userId }: FloatingGroupReturnProps) {
  const [activeGroup, setActiveGroup] = useState<ActiveGroup | null>(null)
  const [availableGroups, setAvailableGroups] = useState<GroupOption[]>([])
  const [isVisible, setIsVisible] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [showGroupSelector, setShowGroupSelector] = useState(false)
  
  const supabase = createSupabaseClient()

  // Check for active waiting groups
  useEffect(() => {
    if (!userId) return

    const checkActiveGroups = async () => {
      try {
        // Find all active groups where user is a member
        const { data: groups } = await supabase
          .from('genyus_group_chats')
          .select(`
            id,
            name,
            invite_code,
            created_at,
            genyus_group_members!inner(user_id, is_active),
            genyus_group_messages(id)
          `)
          .eq('genyus_group_members.user_id', userId)
          .eq('genyus_group_members.is_active', true)
          .eq('is_active', true)
          .order('created_at', { ascending: false })

        if (groups && groups.length > 0) {
          // Process all groups and categorize them
          const processedGroups = groups.map(group => {
            const activeMembers = group.genyus_group_members.filter((m: any) => m.is_active)
            const hasMessages = group.genyus_group_messages.length > 0
            const memberCount = activeMembers.length
            const isRecent = new Date(group.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours

            return {
              id: group.id,
              name: group.name,
              memberCount,
              hasMessages,
              isRecent,
              inviteCode: group.invite_code,
              // Score: prioritize groups with multiple members or messages, then by creation date
              score: (memberCount > 1 ? 100 : 0) + (hasMessages ? 50 : 0) + (isRecent ? 25 : 0) + new Date(group.created_at).getTime() / 1000000
            }
          }).sort((a, b) => b.score - a.score) // Highest score first

          // Set available groups for selector
          setAvailableGroups(processedGroups.map(g => ({
            id: g.id,
            name: g.name,
            memberCount: g.memberCount,
            hasMessages: g.hasMessages,
            isRecent: g.isRecent
          })))

          if (processedGroups.length > 0) {
            const topGroup = processedGroups[0] // Highest scoring group for the main notification

            setActiveGroup({
              id: topGroup.id,
              name: topGroup.name,
              memberCount: topGroup.memberCount,
              inviteCode: topGroup.inviteCode,
              hasMessages: topGroup.hasMessages
            })

            // Show if not on the genyus page and there are active/interesting groups
            const isOnGenyusPage = window.location.pathname.includes('/genyus')
            const hasActiveGroups = processedGroups.some(g => g.memberCount > 1 || g.hasMessages || g.isRecent)
            setIsVisible(!isOnGenyusPage && hasActiveGroups)
          } else {
            setActiveGroup(null)
            setAvailableGroups([])
            setIsVisible(false)
          }
        } else {
          setActiveGroup(null)
          setIsVisible(false)
        }
      } catch (error) {
        console.error('Error checking active groups:', error)
      }
    }

    // Check immediately
    checkActiveGroups()

    // Check every 10 seconds
    const interval = setInterval(checkActiveGroups, 10000)

    // Listen for page navigation
    const handleLocationChange = () => {
      setTimeout(checkActiveGroups, 500) // Small delay for page load
    }

    // Listen for popstate (back/forward navigation)
    window.addEventListener('popstate', handleLocationChange)

    return () => {
      clearInterval(interval)
      window.removeEventListener('popstate', handleLocationChange)
    }
  }, [userId, supabase])

  // Hide when on genyus page
  useEffect(() => {
    const handleLocationChange = () => {
      const isOnGenyusPage = window.location.pathname.includes('/genyus')
      if (isOnGenyusPage) {
        setIsVisible(false)
      } else if (activeGroup && !activeGroup.hasMessages) {
        setIsVisible(true)
      }
    }

    // Check current location
    handleLocationChange()

    // Listen for navigation changes
    const observer = new MutationObserver(() => {
      handleLocationChange()
    })

    observer.observe(document.body, { childList: true, subtree: true })

    return () => observer.disconnect()
  }, [activeGroup])

  const handleReturn = () => {
    if (availableGroups.length === 1) {
      // If only one group, go directly to it
      window.location.href = `/genyus?group=${activeGroup?.id}`
    } else {
      // If multiple groups, show selector
      setShowGroupSelector(true)
    }
  }

  const handleGroupSelect = (groupId: string) => {
    window.location.href = `/genyus?group=${groupId}`
  }

  const handleDismiss = () => {
    setIsVisible(false)
    // Remember dismissal for this session
    sessionStorage.setItem(`dismissed-group-${activeGroup?.id}`, 'true')
  }

  // Check if user dismissed this group in current session
  useEffect(() => {
    if (activeGroup) {
      const isDismissed = sessionStorage.getItem(`dismissed-group-${activeGroup.id}`)
      if (isDismissed) {
        setIsVisible(false)
      }
    }
  }, [activeGroup])

  if (!isVisible || !activeGroup) return null

  return (
    <div className={`fixed bottom-6 right-6 z-50 transition-all duration-300 ${
      isMinimized ? 'transform scale-75' : ''
    }`}>
      <div className="bg-blue-600 text-white rounded-2xl shadow-2xl border border-blue-500 max-w-sm">
        {isMinimized ? (
          // Minimized state
          <button
            onClick={() => setIsMinimized(false)}
            className="p-3 flex items-center space-x-2 hover:bg-blue-700 rounded-2xl transition-colors"
          >
            <div className="w-3 h-3 bg-blue-300 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">Waiting for friend...</span>
          </button>
        ) : (
          // Full state
          <div className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-300 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Active Group Chat</span>
              </div>
              <div className="flex space-x-1">
                <button
                  onClick={() => setIsMinimized(true)}
                  className="text-blue-200 hover:text-white transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                <button
                  onClick={handleDismiss}
                  className="text-blue-200 hover:text-white transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="mb-3">
              <h3 className="font-bold text-white">
                {availableGroups.length === 1 ? activeGroup.name : 'Group Chats Available'}
              </h3>
              <p className="text-blue-100 text-sm">
                {availableGroups.length === 1
                  ? `${activeGroup.memberCount} member${activeGroup.memberCount !== 1 ? 's' : ''} • ${activeGroup.hasMessages ? 'Active conversation' : 'Waiting for messages'}`
                  : `${availableGroups.length} group chats available`
                }
              </p>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleReturn}
                className="flex-1 bg-white text-blue-600 py-2 px-3 rounded-lg font-medium text-sm hover:bg-blue-50 transition-colors"
              >
                {availableGroups.length === 1 ? 'Join Chat' : 'Choose Group'}
              </button>
              {availableGroups.length === 1 && (
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(`${window.location.origin}/genyus/group/join/${activeGroup.inviteCode}`)
                    // Could show a toast here
                  }}
                  className="bg-blue-500 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-400 transition-colors"
                >
                  📋
                </button>
              )}
            </div>

            {availableGroups.length === 1 && (
              <div className="mt-2 text-center">
                <span className="text-xs text-blue-200">
                  {activeGroup.hasMessages ? 'Conversation active' : 'Click 📋 to copy invite link'}
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Group Selector Modal */}
      {showGroupSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-96 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-gray-900">Choose Group Chat</h3>
                <button
                  onClick={() => setShowGroupSelector(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="max-h-80 overflow-y-auto">
              {availableGroups.map((group) => (
                <button
                  key={group.id}
                  onClick={() => handleGroupSelect(group.id)}
                  className="w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{group.name}</h4>
                      <p className="text-sm text-gray-500">
                        {group.memberCount} member{group.memberCount !== 1 ? 's' : ''} •
                        {group.hasMessages ? ' Active conversation' : ' No messages yet'}
                        {group.isRecent && ' • New'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {group.hasMessages && (
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      )}
                      {group.isRecent && (
                        <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">New</span>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
