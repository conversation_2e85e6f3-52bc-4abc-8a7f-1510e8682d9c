{"name": "onlydiary", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "mkdir -p public && (cp node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/pdf.worker.min.js 2>/dev/null || cp node_modules/pdfjs-dist/build/pdf.worker.min.js public/pdf.worker.min.js 2>/dev/null || echo 'PDF worker not found, skipping copy')", "codebook": "node scripts/update-changelog.js", "codebook:week": "node scripts/update-changelog.js --since='1 week ago'", "codebook:month": "node scripts/update-changelog.js --since='1 month ago'"}, "dependencies": {"@anthropic-ai/sdk": "^0.60.0", "@aws-sdk/client-rekognition": "^3.821.0", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@google-ai/generativelanguage": "^3.3.0", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "epub-gen": "^0.1.0", "epub2": "^3.0.2", "framer-motion": "^12.15.0", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "mammoth": "^1.9.1", "natural": "^8.1.0", "next": "15.3.3", "openai": "^5.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "readability-score": "^1.0.1", "reading-time": "^1.5.0", "resend": "^4.5.2", "rtf-parser": "^1.3.3", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "tus-js-client": "^4.3.1", "uuid": "^11.1.0", "web-push": "^3.6.7", "xml2js": "^0.6.2", "zod": "^4.1.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}