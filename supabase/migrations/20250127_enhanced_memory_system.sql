-- Enhanced Memory System for OnlyGenyus
-- Migration: 20250127_enhanced_memory_system.sql

-- User memory profiles - comprehensive context storage
CREATE TABLE IF NOT EXISTS genyus_user_memory (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Communication style analysis
  communication_style JSONB DEFAULT '{}', -- vocabulary_level, formality, humor_preference, etc.
  preferred_response_length TEXT DEFAULT 'adaptive', -- short, medium, long, adaptive
  preferred_tone TEXT DEFAULT 'encouraging', -- encouraging, professional, casual, academic
  
  -- Relationship building
  relationship_stage TEXT DEFAULT 'new', -- new, developing, established, trusted
  trust_level INTEGER DEFAULT 1 CHECK (trust_level >= 1 AND trust_level <= 10),
  interaction_count INTEGER DEFAULT 0,
  positive_feedback_count INTEGER DEFAULT 0,
  
  -- User context and preferences
  interests JSONB DEFAULT '[]', -- Array of topics user is interested in
  expertise_areas JSONB DEFAULT '[]', -- Areas where user has shown knowledge
  goals JSONB DEFAULT '[]', -- User's stated or inferred goals
  challenges JSONB DEFAULT '[]', -- Challenges user has mentioned
  
  -- Learning and adaptation
  successful_response_patterns JSONB DEFAULT '{}', -- What works well for this user
  communication_adaptations JSONB DEFAULT '{}', -- How we've adapted for this user
  
  -- Conversation context
  recent_topics JSONB DEFAULT '[]', -- Last 10 topics discussed
  ongoing_projects JSONB DEFAULT '[]', -- Projects or goals user is working on
  follow_up_items JSONB DEFAULT '[]', -- Things to remember for next conversation
  
  -- Metadata
  last_interaction TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Conversation sessions - group related messages
CREATE TABLE IF NOT EXISTS genyus_conversation_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT, -- Auto-generated or user-provided session title
  topic_category TEXT, -- work, personal, learning, creative, etc.
  session_summary TEXT, -- AI-generated summary of the session
  key_insights JSONB DEFAULT '[]', -- Important insights from this session
  action_items JSONB DEFAULT '[]', -- Things user wanted to do/remember
  started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ended_at TIMESTAMPTZ,
  message_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enhanced conversation context storage
CREATE TABLE IF NOT EXISTS genyus_conversation_context (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id UUID REFERENCES genyus_conversation_sessions(id) ON DELETE CASCADE,
  request_id UUID NOT NULL REFERENCES genyus_requests(id) ON DELETE CASCADE,
  
  -- Message analysis
  message_type TEXT DEFAULT 'question', -- question, follow_up, clarification, feedback
  emotional_tone TEXT, -- excited, frustrated, curious, confident, etc.
  complexity_level INTEGER CHECK (complexity_level >= 1 AND complexity_level <= 10),
  
  -- Context extraction
  mentioned_topics JSONB DEFAULT '[]', -- Topics mentioned in this message
  mentioned_people JSONB DEFAULT '[]', -- People mentioned
  mentioned_projects JSONB DEFAULT '[]', -- Projects or goals mentioned
  temporal_references JSONB DEFAULT '[]', -- Time-based references (yesterday, next week, etc.)
  
  -- User feedback signals
  satisfaction_indicators JSONB DEFAULT '{}', -- Positive/negative signals from user
  follow_up_requested BOOLEAN DEFAULT FALSE,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User feedback and learning
CREATE TABLE IF NOT EXISTS genyus_user_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  request_id UUID REFERENCES genyus_requests(id) ON DELETE CASCADE,
  
  -- Explicit feedback
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback_type TEXT, -- helpful, unhelpful, too_long, too_short, wrong_tone, etc.
  feedback_text TEXT,
  
  -- Implicit feedback signals
  response_time_seconds INTEGER, -- How long user took to respond
  continued_conversation BOOLEAN DEFAULT FALSE, -- Did they ask follow-up?
  shared_response BOOLEAN DEFAULT FALSE, -- Did they share the response?
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Long-term memory insights
CREATE TABLE IF NOT EXISTS genyus_memory_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Insight details
  insight_type TEXT NOT NULL, -- communication_pattern, interest_discovery, goal_identification, etc.
  insight_data JSONB NOT NULL,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
  
  -- Supporting evidence
  supporting_messages JSONB DEFAULT '[]', -- Array of request IDs that support this insight
  first_observed TIMESTAMPTZ NOT NULL,
  last_confirmed TIMESTAMPTZ NOT NULL,
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_genyus_user_memory_user_id ON genyus_user_memory(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_sessions_user_id ON genyus_conversation_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_sessions_started_at ON genyus_conversation_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_context_user_id ON genyus_conversation_context(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_context_session_id ON genyus_conversation_context(session_id);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_context_request_id ON genyus_conversation_context(request_id);
CREATE INDEX IF NOT EXISTS idx_genyus_user_feedback_user_id ON genyus_user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_memory_insights_user_id ON genyus_memory_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_memory_insights_type ON genyus_memory_insights(insight_type);

-- RLS Policies
ALTER TABLE genyus_user_memory ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_conversation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_conversation_context ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_user_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_memory_insights ENABLE ROW LEVEL SECURITY;

-- Users can only access their own memory data
CREATE POLICY "Users can view their own memory" ON genyus_user_memory
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own memory" ON genyus_user_memory
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memory" ON genyus_user_memory
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own conversation sessions" ON genyus_conversation_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own conversation context" ON genyus_conversation_context
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own feedback" ON genyus_user_feedback
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own feedback" ON genyus_user_feedback
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own memory insights" ON genyus_memory_insights
  FOR SELECT USING (auth.uid() = user_id);

-- Service role policies for AI system to read/write memory data
CREATE POLICY "Service role can manage all memory data" ON genyus_user_memory
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage conversation sessions" ON genyus_conversation_sessions
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage conversation context" ON genyus_conversation_context
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage user feedback" ON genyus_user_feedback
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage memory insights" ON genyus_memory_insights
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Functions for memory management
CREATE OR REPLACE FUNCTION update_user_memory_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.last_interaction = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_genyus_user_memory_timestamp
  BEFORE UPDATE ON genyus_user_memory
  FOR EACH ROW
  EXECUTE FUNCTION update_user_memory_timestamp();

-- Conversation summaries for compressed memory
CREATE TABLE IF NOT EXISTS genyus_conversation_summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  timeframe TEXT NOT NULL, -- "Week of Jan 15-21, 2025"
  topic_focus JSONB DEFAULT '[]', -- Main topics discussed
  key_insights JSONB DEFAULT '[]', -- Important insights from this period
  relationship_progress TEXT, -- How relationship evolved
  important_mentions JSONB DEFAULT '[]', -- Key things mentioned
  compressed_content TEXT NOT NULL, -- AI-generated summary
  original_message_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add archived flag to requests for compression management
ALTER TABLE genyus_requests ADD COLUMN IF NOT EXISTS archived BOOLEAN DEFAULT FALSE;

-- Indexes for compression system
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_summaries_user_id ON genyus_conversation_summaries(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_conversation_summaries_timeframe ON genyus_conversation_summaries(timeframe);
CREATE INDEX IF NOT EXISTS idx_genyus_requests_archived ON genyus_requests(archived);

-- RLS for summaries
ALTER TABLE genyus_conversation_summaries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own conversation summaries" ON genyus_conversation_summaries
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage conversation summaries" ON genyus_conversation_summaries
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Add first month tracking to word balance
ALTER TABLE genyus_user_words ADD COLUMN IF NOT EXISTS is_first_month BOOLEAN DEFAULT TRUE;
ALTER TABLE genyus_user_memory ADD COLUMN IF NOT EXISTS onboarding_insights JSONB DEFAULT '{}';
ALTER TABLE genyus_user_memory ADD COLUMN IF NOT EXISTS last_insight_update TIMESTAMPTZ;
ALTER TABLE genyus_user_memory ADD COLUMN IF NOT EXISTS memory_allocation TEXT DEFAULT 'standard';
ALTER TABLE genyus_user_memory ADD COLUMN IF NOT EXISTS last_optimization TIMESTAMPTZ;
ALTER TABLE genyus_user_memory ADD COLUMN IF NOT EXISTS relationship_milestones JSONB DEFAULT '[]';

-- Updated function to initialize user memory profile
CREATE OR REPLACE FUNCTION initialize_user_memory(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO genyus_user_memory (user_id)
  VALUES (user_uuid)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Updated function to handle first month vs monthly reset
CREATE OR REPLACE FUNCTION genyus_reset_monthly_words_enhanced()
RETURNS INT AS $$
DECLARE
  reset_count INT;
BEGIN
  -- Reset first month users to regular monthly allowance
  UPDATE genyus_user_words
  SET words_remaining = 3000,
      last_reset = NOW(),
      updated_at = NOW(),
      is_first_month = FALSE
  WHERE tier = 'free'
    AND is_first_month = TRUE
    AND last_reset < DATE_TRUNC('month', NOW());

  -- Reset regular monthly users
  UPDATE genyus_user_words
  SET words_remaining = 3000,
      last_reset = NOW(),
      updated_at = NOW()
  WHERE tier = 'free'
    AND is_first_month = FALSE
    AND last_reset < DATE_TRUNC('month', NOW());

  GET DIAGNOSTICS reset_count = ROW_COUNT;
  RETURN reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
