-- Allow group creators to delete their own OnlyGenyus groups
-- Date: 2025-08-28

ALTER TABLE IF EXISTS public.genyus_group_chats ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename = 'genyus_group_chats'
      AND policyname = 'Group creators can delete their groups'
  ) THEN
    CREATE POLICY "Group creators can delete their groups"
      ON public.genyus_group_chats
      FOR DELETE
      USING (auth.uid() = creator_user_id);
  END IF;
END $$;
