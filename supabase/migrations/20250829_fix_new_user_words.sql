-- Fix new user word allocation to match config (20k for first month)
-- Date: 2025-08-29

-- Update the initialization function to give new users 20k words for first month
CREATE OR REPLACE FUNCTION genyus_initialize_user_words(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO genyus_user_words (user_id, words_remaining, tier, is_first_month)
  VALUES (p_user_id, 20000, 'free', true)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add is_first_month column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'genyus_user_words' 
    AND column_name = 'is_first_month'
  ) THEN
    ALTER TABLE genyus_user_words 
    ADD COLUMN is_first_month BOOLEAN DEFAULT true;
  END IF;
END $$;

-- Update existing users who have 5000 or fewer words to get the proper first month allocation
UPDATE genyus_user_words 
SET words_remaining = 20000, 
    is_first_month = true,
    updated_at = NOW()
WHERE tier = 'free' 
  AND words_remaining <= 5000 
  AND (is_first_month IS NULL OR is_first_month = true);

-- Create a function to auto-initialize words for new users when they first access Genyus
CREATE OR REPLACE FUNCTION ensure_user_has_words(p_user_id UUID)
RETURNS TABLE(words_remaining INT, tier TEXT) AS $$
DECLARE
  user_words RECORD;
BEGIN
  -- Try to get existing record
  SELECT guw.words_remaining, guw.tier INTO user_words
  FROM genyus_user_words guw
  WHERE guw.user_id = p_user_id;
  
  -- If no record exists, create one
  IF NOT FOUND THEN
    PERFORM genyus_initialize_user_words(p_user_id);
    SELECT guw.words_remaining, guw.tier INTO user_words
    FROM genyus_user_words guw
    WHERE guw.user_id = p_user_id;
  END IF;
  
  -- Return the user's word balance
  RETURN QUERY SELECT user_words.words_remaining, user_words.tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the new function
GRANT EXECUTE ON FUNCTION ensure_user_has_words TO authenticated;
