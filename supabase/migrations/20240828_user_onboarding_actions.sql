-- Create user_onboarding_actions table for tracking Social Media 2.0 onboarding
CREATE TABLE IF NOT EXISTS user_onboarding_actions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  source TEXT NOT NULL CHECK (source IN ('group_chat', 'signup', 'manual')),
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_onboarding_actions_user_id ON user_onboarding_actions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_onboarding_actions_action ON user_onboarding_actions(action);
CREATE INDEX IF NOT EXISTS idx_user_onboarding_actions_source ON user_onboarding_actions(source);
CREATE INDEX IF NOT EXISTS idx_user_onboarding_actions_completed_at ON user_onboarding_actions(completed_at);

-- Enable RLS
ALTER TABLE user_onboarding_actions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own onboarding actions" ON user_onboarding_actions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own onboarding actions" ON user_onboarding_actions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own onboarding actions" ON user_onboarding_actions
  FOR UPDATE USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON user_onboarding_actions TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
