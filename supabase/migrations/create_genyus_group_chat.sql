-- OnlyGenyus Group Chat System
-- Extends existing genyus system to support AI group conversations
-- Migration: create_genyus_group_chat.sql

-- Group chats table
CREATE TABLE IF NOT EXISTS genyus_group_chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL, -- User-defined group name like "Study Group" or "Work Team"
  creator_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  invite_code TEXT UNIQUE NOT NULL, -- Shareable invite code like OnlyDuo
  is_active BOOLEAN DEFAULT true, -- Can be deactivated if needed
  max_members INTEGER DEFAULT 2, -- Limit group size for performance
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Group members table
CREATE TABLE IF NOT EXISTS genyus_group_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID NOT NULL REFERENCES genyus_group_chats(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true, -- Can leave/be removed from group
  
  -- Ensure one membership per user per group
  UNIQUE(group_id, user_id)
);

-- Group messages table (extends genyus_requests for group context)
CREATE TABLE IF NOT EXISTS genyus_group_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID NOT NULL REFERENCES genyus_group_chats(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  message_type TEXT NOT NULL CHECK (message_type IN ('user', 'assistant')),
  content TEXT NOT NULL,
  
  -- Link to existing genyus system for AI responses
  genyus_request_id UUID REFERENCES genyus_requests(id) ON DELETE SET NULL,
  genyus_answer_id UUID REFERENCES genyus_answers(id) ON DELETE SET NULL,
  
  -- Token tracking per user message
  tokens_used INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Group invitations table (similar to OnlyDuo invites)
CREATE TABLE IF NOT EXISTS genyus_group_invites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID NOT NULL REFERENCES genyus_group_chats(id) ON DELETE CASCADE,
  inviter_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  invitee_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL for external invites
  invitee_email TEXT, -- For external invites
  invite_code TEXT NOT NULL, -- Same as group invite_code
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days')
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_genyus_group_chats_creator ON genyus_group_chats(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_group_chats_invite_code ON genyus_group_chats(invite_code);
CREATE INDEX IF NOT EXISTS idx_genyus_group_members_group_id ON genyus_group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_genyus_group_members_user_id ON genyus_group_members(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_group_messages_group_id ON genyus_group_messages(group_id);
CREATE INDEX IF NOT EXISTS idx_genyus_group_messages_created_at ON genyus_group_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_genyus_group_invites_group_id ON genyus_group_invites(group_id);
CREATE INDEX IF NOT EXISTS idx_genyus_group_invites_invitee_user ON genyus_group_invites(invitee_user_id);

-- RLS Policies
ALTER TABLE genyus_group_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_invites ENABLE ROW LEVEL SECURITY;

-- Group chats: Members can view groups they belong to
CREATE POLICY "Users can view groups they are members of" ON genyus_group_chats
  FOR SELECT USING (
    id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Users can create group chats" ON genyus_group_chats
  FOR INSERT WITH CHECK (auth.uid() = creator_user_id);

CREATE POLICY "Group creators can update their groups" ON genyus_group_chats
  FOR UPDATE USING (auth.uid() = creator_user_id);

-- Group members: Members can view other members in their groups
CREATE POLICY "Users can view members of groups they belong to" ON genyus_group_members
  FOR SELECT USING (
    group_id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Users can join groups (insert membership)" ON genyus_group_members
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave groups (update their membership)" ON genyus_group_members
  FOR UPDATE USING (auth.uid() = user_id);

-- Group messages: Members can view and send messages in their groups
CREATE POLICY "Users can view messages in groups they belong to" ON genyus_group_messages
  FOR SELECT USING (
    group_id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Users can send messages to groups they belong to" ON genyus_group_messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    group_id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- Group invites: Users can view invites sent to them or sent by them
CREATE POLICY "Users can view invites sent to them or by them" ON genyus_group_invites
  FOR SELECT USING (
    auth.uid() = inviter_user_id OR 
    auth.uid() = invitee_user_id
  );

CREATE POLICY "Users can create group invites for groups they belong to" ON genyus_group_invites
  FOR INSERT WITH CHECK (
    auth.uid() = inviter_user_id AND
    group_id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Users can update invites sent to them" ON genyus_group_invites
  FOR UPDATE USING (auth.uid() = invitee_user_id);

-- Functions for group management
CREATE OR REPLACE FUNCTION check_group_token_eligibility(group_id_param UUID)
RETURNS TABLE(user_id UUID, words_remaining INT, is_eligible BOOLEAN) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    gm.user_id,
    COALESCE(guw.words_remaining, 0) as words_remaining,
    CASE 
      WHEN COALESCE(guw.words_remaining, 0) >= 1000 OR COALESCE(guw.words_remaining, 0) = -1 
      THEN true 
      ELSE false 
    END as is_eligible
  FROM genyus_group_members gm
  LEFT JOIN genyus_user_words guw ON gm.user_id = guw.user_id
  WHERE gm.group_id = group_id_param AND gm.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate unique invite codes
CREATE OR REPLACE FUNCTION generate_group_invite_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists BOOLEAN;
BEGIN
  LOOP
    -- Generate 8-character alphanumeric code
    code := upper(substring(md5(random()::text) from 1 for 8));
    
    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM genyus_group_chats WHERE invite_code = code) INTO exists;
    
    -- If code doesn't exist, return it
    IF NOT exists THEN
      RETURN code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
