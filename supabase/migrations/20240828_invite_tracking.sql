-- Add invite tracking columns to genyus_group_invites table
ALTER TABLE genyus_group_invites 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'clicked', 'accepted', 'expired')),
ADD COLUMN IF NOT EXISTS clicked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS accepted_at TIMESTAMP WITH TIME ZONE;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_genyus_group_invites_status ON genyus_group_invites(status);
CREATE INDEX IF NOT EXISTS idx_genyus_group_invites_clicked_at ON genyus_group_invites(clicked_at);

-- Create invite_link_clicks table for detailed tracking
CREATE TABLE IF NOT EXISTS invite_link_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invite_code TEXT NOT NULL,
  group_id UUID REFERENCES genyus_group_chats(id) ON DELETE CASCADE,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL -- Set when user is identified
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_invite_link_clicks_invite_code ON invite_link_clicks(invite_code);
CREATE INDEX IF NOT EXISTS idx_invite_link_clicks_group_id ON invite_link_clicks(group_id);
CREATE INDEX IF NOT EXISTS idx_invite_link_clicks_clicked_at ON invite_link_clicks(clicked_at);

-- Enable RLS
ALTER TABLE invite_link_clicks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for invite_link_clicks
CREATE POLICY "Group members can view invite clicks for their groups" ON invite_link_clicks
  FOR SELECT USING (
    group_id IN (
      SELECT group_id FROM genyus_group_members 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Anyone can insert invite clicks" ON invite_link_clicks
  FOR INSERT WITH CHECK (true);

-- Grant permissions
GRANT ALL ON invite_link_clicks TO authenticated;
GRANT ALL ON invite_link_clicks TO anon; -- Allow anonymous tracking
