-- Complete OnlyGenyus Group Chat Fix: RLS, Word Initialization, and Join Flow
-- Date: 2025-08-29
-- This migration fixes all issues preventing group chat from working for new users

-- 1. WORD BALANCE INITIALIZATION
-- Fix new user word allocation to match config (20k for first month)
CREATE OR REPLACE FUNCTION genyus_initialize_user_words(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO genyus_user_words (user_id, words_remaining, tier, is_first_month)
  VALUES (p_user_id, 20000, 'free', true)
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add is_first_month column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'genyus_user_words'
    AND column_name = 'is_first_month'
  ) THEN
    ALTER TABLE genyus_user_words
    ADD COLUMN is_first_month BOOLEAN DEFAULT true;
  END IF;
END $$;

-- Create auto-initialization function for API calls
CREATE OR REPLACE FUNCTION ensure_user_has_words(p_user_id UUID)
RETURNS TABLE(words_remaining INT, tier TEXT) AS $$
DECLARE
  user_words RECORD;
BEGIN
  -- Try to get existing record
  SELECT guw.words_remaining, guw.tier INTO user_words
  FROM genyus_user_words guw
  WHERE guw.user_id = p_user_id;

  -- If no record exists, create one
  IF NOT FOUND THEN
    PERFORM genyus_initialize_user_words(p_user_id);
    SELECT guw.words_remaining, guw.tier INTO user_words
    FROM genyus_user_words guw
    WHERE guw.user_id = p_user_id;
  END IF;

  -- Return the user's word balance
  RETURN QUERY SELECT user_words.words_remaining, user_words.tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION ensure_user_has_words TO authenticated;

-- 2. RESET ALL RLS POLICIES TO PREVENT CONFLICTS
-- Disable RLS temporarily to clear all policies
ALTER TABLE genyus_group_chats DISABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_invites DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies to prevent conflicts
DROP POLICY IF EXISTS "Authenticated users can view active groups to join" ON genyus_group_chats;
DROP POLICY IF EXISTS "Users can view group members for groups they belong to" ON genyus_group_members;
DROP POLICY IF EXISTS "Allow viewing members of active groups" ON genyus_group_members;
DROP POLICY IF EXISTS "Users can join groups" ON genyus_group_members;
DROP POLICY IF EXISTS "Users can update own membership" ON genyus_group_members;
DROP POLICY IF EXISTS "View active groups" ON genyus_group_chats;
DROP POLICY IF EXISTS "View group members" ON genyus_group_members;
DROP POLICY IF EXISTS "Join groups" ON genyus_group_members;
DROP POLICY IF EXISTS "Update own membership" ON genyus_group_members;
DROP POLICY IF EXISTS "View group messages" ON genyus_group_messages;
DROP POLICY IF EXISTS "Send group messages" ON genyus_group_messages;
DROP POLICY IF EXISTS "View group members simple" ON genyus_group_members;
DROP POLICY IF EXISTS "Anyone can view members of active groups" ON genyus_group_members;
DROP POLICY IF EXISTS "Anyone can view active groups" ON genyus_group_chats;
DROP POLICY IF EXISTS "Anyone can view members" ON genyus_group_members;
DROP POLICY IF EXISTS "Manage own membership" ON genyus_group_members;
DROP POLICY IF EXISTS "Access group messages" ON genyus_group_messages;
DROP POLICY IF EXISTS "Manage group invites" ON genyus_group_invites;

-- Re-enable RLS
ALTER TABLE genyus_group_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_group_invites ENABLE ROW LEVEL SECURITY;

-- 3. CREATE SIMPLE, WORKING RLS POLICIES

-- Groups: Allow viewing active groups (needed for join page and group list)
CREATE POLICY "View active groups"
  ON genyus_group_chats
  FOR SELECT
  USING (is_active = true AND auth.uid() IS NOT NULL);

-- Groups: Allow creators to manage their groups
CREATE POLICY "Manage own groups"
  ON genyus_group_chats
  FOR ALL
  USING (creator_user_id = auth.uid());

-- Members: Allow viewing all members of active groups (needed for join page member counts)
CREATE POLICY "View members of active groups"
  ON genyus_group_members
  FOR SELECT
  USING (
    auth.uid() IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM genyus_group_chats gc
      WHERE gc.id = genyus_group_members.group_id
      AND gc.is_active = true
    )
  );

-- Members: Allow users to manage their own membership
CREATE POLICY "Manage own membership"
  ON genyus_group_members
  FOR ALL
  USING (user_id = auth.uid());

-- Messages: Allow viewing/sending messages in groups where user is an active member
CREATE POLICY "Access group messages"
  ON genyus_group_messages
  FOR ALL
  USING (
    auth.uid() IS NOT NULL AND (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM genyus_group_members gm
        WHERE gm.group_id = genyus_group_messages.group_id
        AND gm.user_id = auth.uid()
        AND gm.is_active = true
      )
    )
  );

-- Invites: Allow managing invites for active groups
CREATE POLICY "Manage invites"
  ON genyus_group_invites
  FOR ALL
  USING (auth.uid() IS NOT NULL);

