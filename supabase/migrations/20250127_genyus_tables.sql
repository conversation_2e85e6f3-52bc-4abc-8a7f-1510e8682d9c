-- Genyus AI Chat System Tables
-- Migration: 20250127_genyus_tables.sql

-- Users' word balances
CREATE TABLE IF NOT EXISTS genyus_user_words (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  words_remaining INT NOT NULL DEFAULT 0,
  tier TEXT NOT NULL DEFAULT 'free', -- free|starter|pro|power
  last_reset TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Requests
CREATE TABLE IF NOT EXISTS genyus_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Provider raw responses (for transparency panel)
CREATE TABLE IF NOT EXISTS genyus_provider_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id UUID NOT NULL REFERENCES genyus_requests(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,    -- 'openai' | 'google' | 'anthropic'
  model TEXT NOT NULL,
  latency_ms INT,
  tokens_in INT,
  tokens_out INT,
  raw_text TEXT,
  normalized_text TEXT,
  error TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Final moderated answer
CREATE TABLE IF NOT EXISTS genyus_answers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id UUID NOT NULL REFERENCES genyus_requests(id) ON DELETE CASCADE,
  moderator_model TEXT NOT NULL,
  final_text TEXT NOT NULL,
  latency_ms INT,
  tokens_in INT,
  tokens_out INT,
  word_count INT NOT NULL,
  cost_usd NUMERIC(8,4) DEFAULT 0,
  safety_flags JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Purchases (to reconcile Stripe webhooks)
CREATE TABLE IF NOT EXISTS genyus_word_purchases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_session_id TEXT UNIQUE NOT NULL,
  price_id TEXT NOT NULL,
  words_granted INT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_genyus_requests_user_id ON genyus_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_requests_created_at ON genyus_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_genyus_provider_responses_request_id ON genyus_provider_responses(request_id);
CREATE INDEX IF NOT EXISTS idx_genyus_answers_request_id ON genyus_answers(request_id);
CREATE INDEX IF NOT EXISTS idx_genyus_word_purchases_user_id ON genyus_word_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_genyus_word_purchases_stripe_session ON genyus_word_purchases(stripe_session_id);

-- RLS Policies
ALTER TABLE genyus_user_words ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_provider_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE genyus_word_purchases ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view their own word balance" ON genyus_user_words
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own word balance" ON genyus_user_words
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own word balance" ON genyus_user_words
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own requests" ON genyus_requests
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own requests" ON genyus_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own provider responses" ON genyus_provider_responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM genyus_requests 
      WHERE genyus_requests.id = genyus_provider_responses.request_id 
      AND genyus_requests.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own answers" ON genyus_answers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM genyus_requests 
      WHERE genyus_requests.id = genyus_answers.request_id 
      AND genyus_requests.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own purchases" ON genyus_word_purchases
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own purchases" ON genyus_word_purchases
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Atomic word decrement function
CREATE OR REPLACE FUNCTION genyus_decrement_words(p_user_id UUID, p_words INT)
RETURNS VOID AS $$
BEGIN
  UPDATE genyus_user_words
  SET words_remaining = words_remaining - p_words,
      updated_at = NOW()
  WHERE user_id = p_user_id AND words_remaining >= p_words;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'INSUFFICIENT_WORDS';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initialize user with free words
CREATE OR REPLACE FUNCTION genyus_initialize_user_words(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO genyus_user_words (user_id, words_remaining, tier)
  VALUES (p_user_id, 5000, 'free')
  ON CONFLICT (user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment user words (for purchases)
CREATE OR REPLACE FUNCTION increment_user_words(p_user_id UUID, p_words INT)
RETURNS VOID AS $$
BEGIN
  UPDATE genyus_user_words
  SET words_remaining = words_remaining + p_words,
      updated_at = NOW()
  WHERE user_id = p_user_id;

  IF NOT FOUND THEN
    INSERT INTO genyus_user_words (user_id, words_remaining, tier)
    VALUES (p_user_id, p_words, 'starter');
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset monthly free words
CREATE OR REPLACE FUNCTION genyus_reset_monthly_words()
RETURNS INT AS $$
DECLARE
  reset_count INT;
BEGIN
  UPDATE genyus_user_words
  SET words_remaining = 5000,
      last_reset = NOW(),
      updated_at = NOW()
  WHERE tier = 'free' 
    AND last_reset < DATE_TRUNC('month', NOW());
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  RETURN reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_genyus_user_words_updated_at
  BEFORE UPDATE ON genyus_user_words
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON genyus_user_words TO authenticated;
GRANT SELECT, INSERT ON genyus_requests TO authenticated;
GRANT SELECT ON genyus_provider_responses TO authenticated;
GRANT SELECT ON genyus_answers TO authenticated;
GRANT SELECT, INSERT ON genyus_word_purchases TO authenticated;
GRANT EXECUTE ON FUNCTION genyus_decrement_words TO authenticated;
GRANT EXECUTE ON FUNCTION genyus_initialize_user_words TO authenticated;
GRANT EXECUTE ON FUNCTION increment_user_words TO authenticated;
