-- Check Duo Timeline Status
-- Run these queries to see if you need the fix

-- 1. Check if there are any completed duo posts
SELECT 
    COUNT(*) as total_completed_duos,
    MIN(created_at) as oldest_completed,
    MAX(created_at) as newest_completed
FROM duo_posts 
WHERE status = 'completed';

-- 2. Check if there are timeline_posts entries for duos
SELECT 
    COUNT(*) as duo_timeline_entries
FROM timeline_posts 
WHERE content_type = 'duo';

-- 3. Check for completed duos that are missing from timeline_posts
SELECT 
    dp.id,
    dp.title,
    dp.status,
    dp.created_at,
    CASE 
        WHEN tp.id IS NOT NULL THEN 'Has timeline entry'
        ELSE 'MISSING from timeline'
    END as timeline_status
FROM duo_posts dp
LEFT JOIN timeline_posts tp ON tp.duo_post_id = dp.id
WHERE dp.status = 'completed'
ORDER BY dp.created_at DESC;

-- 4. Check if the trigger function exists
SELECT 
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_name = 'create_timeline_post_for_duo';

-- 5. Check if the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE trigger_name = 'trigger_duo_timeline_post';

-- Summary query - tells you exactly what you need to know
SELECT 
    (SELECT COUNT(*) FROM duo_posts WHERE status = 'completed') as completed_duos,
    (SELECT COUNT(*) FROM timeline_posts WHERE content_type = 'duo') as duo_timeline_entries,
    (SELECT COUNT(*) FROM duo_posts dp WHERE dp.status = 'completed' AND NOT EXISTS (SELECT 1 FROM timeline_posts tp WHERE tp.duo_post_id = dp.id)) as missing_from_timeline,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = 'create_timeline_post_for_duo') as trigger_function_exists,
    (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name = 'trigger_duo_timeline_post') as trigger_exists;
